import {Button, Dropdown, Form, Input, Modal, toast, Upload} from 'acud';
import React, {memo, useCallback, useContext, useEffect, useMemo, useRef, useState} from 'react';
import {OutlinedUpload} from 'acud-icon';
import {validatePath, isFilelistReasonble, validateFileNameInvalid} from '@utils/utils';
import {useDrop} from 'ahooks';
import ShowUploadFile, {ShowUploadFileRefHandle} from '@components/ShowUploadFile';
import {TaskQueue} from '@utils/taskQueue';
import * as http from '@api/metaRequest';
import FilePathSelect, {PathLevel} from '@components/FilePathSelect';

import './index.less';
import {WorkspaceContext} from '@pages/index';
import flags from '@/flags';
const isPrivate = flags.DatabuilderPrivateSwitch;

interface Directory {
  catalog: string;
  schema: string;
  node: string;
  path: string;
}

interface UploadVolumeModalProps {
  // modal 可见
  visible: boolean;
  // 上传流控制器，建议在父组件使用ref创建并销毁，防止死循环
  controller: AbortController;
  // 默认文件目录
  defaultDirectory?: Directory;
  // 禁用，禁止用户修改文件目录
  disabled?: boolean;
  // 是否展示【浏览】路径选择器
  showPathSelect?: boolean;
  // 关闭弹窗
  onCancel: () => void;
  // 创建成功回调，如刷新列表
  onCreateSuccessCallback: () => void;
  // 关闭掉上传文件进度弹窗后回调，对 controller 进行销毁后创建
  afterCloseUploadFile: () => void;
}

const queue = new TaskQueue(2);

// 更新 file 上传状态/进度
const updateFileItemInfo = (curInfo, id, updateInfo) => {
  return (
    curInfo?.map((item) => {
      if (id === item.file.uid) {
        return {...item, ...updateInfo};
      }
      return item;
    }) || []
  );
};

/**
 * 通用数据卷上传组件，包含目录选择
 */
const UploadVolumeModal: React.FC<UploadVolumeModalProps> = ({
  visible,
  disabled = false,
  showPathSelect = false,
  defaultDirectory,
  controller,
  onCancel,
  onCreateSuccessCallback,
  afterCloseUploadFile
}) => {
  const [form] = Form.useForm();
  const dropRef = useRef(null);
  const uploaderRef = useRef<ShowUploadFileRefHandle>();

  // 控制文件夹模式
  const [isDir, setIsDir] = useState(true);
  const [uploadRequestInfo, setUploadRequestInfo] = useState<any>([]);
  const [fileList, setFileList] = useState<any[]>([]);
  const [directory, setDirectory] = useState<Directory>(null);
  const {workspaceId} = useContext(WorkspaceContext);

  useDrop(document.querySelector('.meta-volume-upload-box .acud-upload-list'), {
    onDrop: (e) => {
      e.preventDefault();
      e.stopPropagation();
      if (e.dataTransfer) {
        const newEvent = new DragEvent('drop', {
          bubbles: true,
          cancelable: true,
          clientX: e.clientX,
          clientY: e.clientY,
          screenX: e.screenX,
          screenY: e.screenY,
          dataTransfer: e.dataTransfer
        });
        document.querySelector('.meta-volume-upload-box .drag-content')?.dispatchEvent(newEvent);
      }
    }
  });

  useEffect(() => {
    setDirectory(defaultDirectory);
  }, [defaultDirectory]);

  const fullName = useMemo(() => {
    if (directory) {
      const {catalog, schema, node} = directory;
      return `${catalog}.${schema}.${node}`;
    }
    return '';
  }, [directory]);

  const volumePath = useMemo(() => {
    if (directory) {
      const {catalog, schema, node, path} = directory;
      return `/Volumes/${catalog}/${schema}/${node}/${path}`;
    }
    return '/Volumes/';
  }, [directory]);

  const defaultVolumePath = useMemo(() => {
    if (defaultDirectory) {
      const {catalog, schema, node, path} = defaultDirectory;
      return `/Volumes/${catalog}/${schema}/${node}/${path}`;
    }
    return '/Volumes/';
  }, [defaultDirectory]);

  const getDirectoryPath = (file: any) => {
    const filePath = file?.originFileObj?.webkitRelativePath;
    const lastIndex = filePath.lastIndexOf('/');
    return ~lastIndex ? filePath.slice(0, lastIndex + 1) : '';
  };

  // 单个文件发起请求上传到后端
  const uploadSingleFile = useCallback(
    async (file: any) => {
      const formData = new FormData();
      formData.set('file', file.originFileObj, file.name);

      setUploadRequestInfo((pre) => updateFileItemInfo(pre, file.originFileObj.uid, {status: 'loading'}));

      const directoryPath = getDirectoryPath(file);
      try {
        const res = await http.uploadVolumeFile(
          workspaceId,
          fullName,
          formData as any,
          `${volumePath}${directoryPath}`,
          (progressEvent) => {
            setUploadRequestInfo((pre) =>
              updateFileItemInfo(pre, file.originFileObj.uid, {
                percent: Math.round((progressEvent.loaded / progressEvent.total) * 100)
              })
            );
          },
          controller
        );
        if (!res.success) {
          throw new Error('Upload failed');
        }
        // 处理文件上传成功，更新 uploadRequestInfo中对应项的 status
        setUploadRequestInfo((pre) => updateFileItemInfo(pre, file.originFileObj.uid, {status: 'success'}));
      } catch (err) {
        // 处理文件上传失败，更新 uploadRequestInfo中对应项的 status
        setUploadRequestInfo((pre) => updateFileItemInfo(pre, file.originFileObj.uid, {status: 'failed'}));
      }
      onCreateSuccessCallback();
    },
    [controller, fullName, onCreateSuccessCallback, volumePath]
  );

  const onModalClose = useCallback(() => {
    setFileList([]);
    onCancel();
  }, [onCancel]);

  // 上传到后端
  const onCreateClick = useCallback(async () => {
    if (!isFilelistReasonble(fileList)) {
      return;
    }
    try {
      await form.validateFields();
    } catch {
      return;
    }
    const arr: any = [];
    fileList.map((item) => {
      const directoryPath = getDirectoryPath(item);

      arr.push({
        file: item.originFileObj,
        path: `${volumePath}${directoryPath}`,
        status: 'loading'
      });
    });
    setUploadRequestInfo((pre) => [...pre, ...arr]);

    for (const file of fileList) {
      queue.add(async () => await uploadSingleFile(file));
    }

    uploaderRef?.current?.expand();

    onModalClose();
  }, [fileList, form, onModalClose, uploadSingleFile, volumePath]);

  const UploadProps = {
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file) => {
      // 私有化：校验是否 带有冒号":"和空格" "的文件名
      if (isPrivate && validateFileNameInvalid(file.name)) {
        toast.error({
          message: `不支持上传有冒号或者空格的文件名`,
          duration: 5
        });
        return Upload.LIST_IGNORE;
      }
      // 这块 上传场景 文件名称长度校验 前端不校验 由后端去校验bos总长度
      // if (!validatePath(file.name)) {
      //   toast.error({
      //     message: '文件名称长度必须在1～1024字节之间，不能以/或者\\字符开头，不能出现连续的//',
      //     duration: 5
      //   });
      //   return Upload.LIST_IGNORE;
      // }
      const isLt100M = file.size <= 1024 * 1024 * 100;
      const totalSize = fileList.reduce((sum, item) => sum + item.size, 0) + file.size;
      const isLt500M = totalSize <= 1024 * 1024 * 500;
      if (!isLt100M) {
        toast.error({
          message: '单个文件要小于100MB',
          duration: 5
        });
        return Upload.LIST_IGNORE;
      }
      if (!isLt500M) {
        toast.error({
          message: '单次上传文件总大小不能超过500MB',
          duration: 5
        });
        return Upload.LIST_IGNORE;
      }
      if (fileList.length >= 100) {
        toast.error({
          message: '最多上传100个文件',
          duration: 5
        });
        return Upload.LIST_IGNORE;
      }
      // 过滤重名文件
      if (fileList.find((item) => item.name === file.name)) {
        return Upload.LIST_IGNORE;
      }
      setFileList((prevList) => [...prevList, file]);
      return false;
    },
    fileList
  };

  // 上传到内存
  const handleChange = (info) => {
    setFileList(info.fileList);
  };

  // 文件路径改变回调
  const onFilePathChange = (value) => {
    const pathArr = value.replace('/Volumes/', '').split('/');
    const [catalog, schema, node, ...path] = pathArr;

    setDirectory({catalog, schema, node, path: path?.join('/')});
  };

  return (
    <>
      <Modal
        closable={true}
        title="将⽂件上传到数据卷"
        okText="上传"
        size="normal"
        visible={visible}
        okButtonProps={{
          disabled: fileList.length === 0
        }}
        onOk={onCreateClick}
        onCancel={onModalClose}
        destroyOnClose
      >
        <div className="meta-volume-upload-box">
          <Upload ref={dropRef} {...UploadProps} multiple onChange={handleChange} directory={isDir}>
            <div
              className="drag-content"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setIsDir(false);
                setTimeout(() => {
                  dropRef.current?.upload?.uploader?.onClick();
                  setIsDir(true);
                }, 10);
              }}
            >
              {fileList.length ? (
                <div className="volume-upload-text volume-upload-text-extra">
                  将文件夹或文件拖拽到文件列表区域可继续添加，也可<Button type="actiontext">点击此处</Button>
                  选择文件
                </div>
              ) : (
                <>
                  <OutlinedUpload fill="#D3D3D3" width={40} />
                  <div className="volume-upload-text">
                    将文件夹或多个文件拖到此处，或
                    <Button type="actiontext">点击上传</Button>
                  </div>
                  <p className="volume-upload-des">
                    * ⽬标⽬录下如果存在同名⽂件，将被新上传的⽂件覆盖
                    <br />* 每次上传的总文件大小不超过500MB，单个文件不超过100MB；每次最多上传100个文件
                  </p>
                </>
              )}
            </div>
          </Upload>
        </div>
        <Form
          className="meta-volume-upload-form"
          labelAlign="left"
          layout="vertical"
          inputMaxWidth="758px"
          name="nest-messages"
          form={form}
        >
          <Form.Item label="上传目录">
            <FilePathSelect
              form={form}
              metaDirs={['volume']}
              onChange={onFilePathChange}
              disabled={disabled}
              showPathSelect={showPathSelect}
              value={defaultVolumePath}
              selectableLevel={PathLevel.Volume}
              hasDoris={false}
            />
          </Form.Item>
        </Form>
      </Modal>
      {/* 上传文件展示组件 */}
      <ShowUploadFile
        ref={uploaderRef}
        uploadList={uploadRequestInfo}
        setUploadListFun={setUploadRequestInfo}
        reUploadRequest={uploadSingleFile}
        afterCloseFn={afterCloseUploadFile}
      />
    </>
  );
};

export default memo(UploadVolumeModal);
