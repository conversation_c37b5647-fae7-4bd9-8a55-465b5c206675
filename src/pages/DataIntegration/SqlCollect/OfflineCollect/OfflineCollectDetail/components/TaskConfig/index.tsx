import React from 'react';
import {JobDetail} from '@api/integration/type';
import {Collapse} from 'acud';
import styles from './index.module.less';
import InfoPanel from '@components/InfoPanel';
import MapSettingTable from '../../../OfflineCollectConfig/page/MapSetting/MapSettingTable';
import {
  DirtyDataStrategyChineseMap,
  SinkModeChineseMap,
  SinkNameRuleEnum,
  SinkNameRuleChineseMap,
  SourceChangeHandleChineseMap,
  DirtyDataStrategyEnum,
  SinkTableTypeChineseMap
} from '../../../../constants';
import IconSvg from '@components/IconSvg';

const {Panel} = Collapse;
const PREFIX = 'task-config';

const getByPath = (obj, paths) => {
  return paths.reduce((o, key) => o?.[key], obj);
};

const MappingInfo = [
  {
    name: '基本信息',
    list: [
      {name: '任务名称', key: ['name']},
      {name: '任务描述', key: ['description']}
    ]
  },
  {
    name: '源端信息',
    list: [
      {name: '源端类型', key: ['sourceConfig', 'sourceType']},
      {name: '数据源名称', key: ['sourceConfig', 'sourceConnectionId']},
      {name: '数据库名称', key: ['sourceConfig', 'sourceDatabase']},
      {name: '数据表名称', key: ['sourceConfig', 'sourceTable']}
    ]
  },
  {
    name: '目标端信息',
    list: [
      // {name: '数据去向', key: ['sinkConfig', 'target']},
      {name: '目标端类型', key: ['sinkConfig', 'sinkType']},
      {
        name: '建表方式',
        key: ['sinkConfig', 'isAutoCreated'],
        convert: (value) => (value === true ? '自动建表' : '选择已有表')
      },
      {
        name: '表类型',
        key: ['sinkConfig', 'sinkTableType'],
        convert: (value) => SinkTableTypeChineseMap[value] || '-'
      },
      {
        name: '表名设置',
        key: ['sinkConfig', 'sinkNameRule'],
        convert: (value) => SinkNameRuleChineseMap[value] || '-'
      },
      {
        name: '前缀',
        key: ['sinkConfig', 'prefix'],
        show: (record) => {
          const sinkNameRule = getByPath(record, ['sinkConfig', 'sinkNameRule']);
          return (
            sinkNameRule === SinkNameRuleEnum.ADDPREFIX ||
            sinkNameRule === SinkNameRuleEnum.ADDPREFIXANDSUFFIX
          );
        }
      },
      {
        name: '后缀',
        key: ['sinkConfig', 'suffix'],
        show: (record) => {
          const sinkNameRule = getByPath(record, ['sinkConfig', 'sinkNameRule']);
          return (
            sinkNameRule === SinkNameRuleEnum.ADDSUFFIX ||
            sinkNameRule === SinkNameRuleEnum.ADDPREFIXANDSUFFIX
          );
        }
      },
      {name: '数据库/表', key: ['sinkConfig', 'sinkPath']},
      {name: '描述', key: ['sinkConfig', 'comment']}
    ]
  }
];

const RunInfo = [
  {
    name: '计算实例信息',
    list: [{name: '计算实例', key: ['compute', 'name']}]
  },
  {
    name: '源端读取信息',
    list: [
      {name: '开启限速', key: ['sourceConfig', 'enableRateLimit'], convert: (value) => (value ? '是' : '否')},
      {
        name: '最大流量速率',
        key: ['sourceConfig', 'rateLimit', 'flow'],
        convert: (value) => `${value}MB/s`,
        show: (record) => getByPath(record, ['sourceConfig', 'enableRateLimit'])
      },
      {
        name: '最大行数速率',
        key: ['sourceConfig', 'rateLimit', 'records'],
        convert: (value) => `${value}行/秒`,
        show: (record) => getByPath(record, ['sourceConfig', 'enableRateLimit'])
      },
      {name: '最大并发数', key: ['sourceConfig', 'parallelism'], convert: (value) => `${value}`},
      {
        name: '分片字段',
        key: ['sourceConfig', 'splitField'],
        show: (record) => getByPath(record, ['sourceConfig', 'parallelism']) > 1
      },
      {
        name: '源端表删除字段',
        key: ['sourceConfig', 'sourceChange', 'onDeleteColumn'],
        convert: (value) => SourceChangeHandleChineseMap[value] || '-'
      },
      {
        name: '源端表新增字段',
        key: ['sourceConfig', 'sourceChange', 'onAddColumn'],
        convert: (value) => SourceChangeHandleChineseMap[value] || '-'
      },
      {
        name: '源端表被删除',
        key: ['sourceConfig', 'sourceChange', 'onDeleteSource'],
        convert: (value) => SourceChangeHandleChineseMap[value] || '-'
      }
    ]
  },
  {
    name: '目标端写入信息',
    list: [
      {
        name: '写入模式',
        key: ['sinkConfig', 'sinkMode'],
        convert: (value, record) => SinkModeChineseMap[value] || '-'
      },
      {
        name: '脏数据策略',
        key: ['sinkConfig', 'dirtyDataStrategy', 'strategy'],
        convert: (value, record) => DirtyDataStrategyChineseMap[value] || '-'
      },
      {
        name: '脏数据容忍度',
        key: ['sinkConfig', 'dirtyDataStrategy', 'maxDirtyRatio'],
        convert: (value) => `${value}%`,
        show: (record) => {
          const strategy = getByPath(record, ['sinkConfig', 'dirtyDataStrategy', 'strategy']);
          const maxDirtyRatio = getByPath(record, ['sinkConfig', 'dirtyDataStrategy', 'maxDirtyRatio']);
          return maxDirtyRatio > 0 && strategy === DirtyDataStrategyEnum.TOLERANT;
        }
      },
      {
        name: '脏数据容忍度',
        key: ['sinkConfig', 'dirtyDataStrategy', 'maxDirtyRow'],
        convert: (value) => `${value}行`,
        show: (record) => {
          const strategy = getByPath(record, ['sinkConfig', 'dirtyDataStrategy', 'strategy']);
          const maxDirtyRow = getByPath(record, ['sinkConfig', 'dirtyDataStrategy', 'maxDirtyRow']);
          return maxDirtyRow > 0 && strategy === DirtyDataStrategyEnum.TOLERANT;
        }
      },
      {
        name: '是否写入脏数据',
        key: ['sinkConfig', 'dirtyDataStrategy', 'enableDirtyDataWrite'],
        convert: (value) => (value ? '是' : '否'),
        show: (record) => {
          const strategy = getByPath(record, ['sinkConfig', 'dirtyDataStrategy', 'strategy']);
          return strategy !== DirtyDataStrategyEnum.TOLERANT;
        }
      },
      {
        name: '脏数据存储路径',
        key: ['sinkConfig', 'dirtyDataStrategy', 'dirtyDataVolume'],
        show: (record) => {
          const dirtyDataVolume = getByPath(record, ['sinkConfig', 'dirtyDataStrategy', 'dirtyDataVolume']);
          return dirtyDataVolume;
        }
      }
    ]
  }
];

/**
 * 任务配置
 * @param jobDetail 任务详情
 */
const TaskConfig: React.FC<{jobDetail?: JobDetail}> = ({jobDetail}) => {
  const renderInfo = (data: any, columns: any[]) => {
    if (!data) {
      return;
    }
    return (
      <div className={styles[`${PREFIX}-info-block`]}>
        {columns.map((item) => {
          const infoList = item.list
            .filter((subItem) => (subItem.show ? subItem.show(data) : true))
            .map((subItem) => ({
              label: subItem.name,
              value: subItem.convert
                ? subItem.convert(getByPath(data, subItem.key))
                : getByPath(data, subItem.key)
            }));
          const title = (
            <div className={styles[`${PREFIX}-info-block-subtitle`]}>
              <span>{item.name}</span>
              <span className={styles[`${PREFIX}-info-block-subtitle-count`]}>共{infoList.length}条</span>
            </div>
          );
          return <InfoPanel key={item.name} infoList={infoList} title={title} />;
        })}
      </div>
    );
  };

  return (
    <div className={styles[`${PREFIX}`]}>
      <Collapse defaultActiveKey={['1', '2', '3']} accordion={false}>
        <Panel
          header={
            <div className={styles['collapse-header']}>
              <IconSvg type="arrow-down" size={16} className="collapse-header-arrow" />
              源端与目标端信息
            </div>
          }
          key="1"
        >
          {renderInfo(jobDetail, MappingInfo)}
        </Panel>
        <Panel
          header={
            <div className={styles['collapse-header']}>
              <IconSvg type="arrow-down" size={16} className="collapse-header-arrow" />
              运行信息
            </div>
          }
          key="2"
        >
          {renderInfo(jobDetail, RunInfo)}
        </Panel>
        <Panel
          header={
            <div className={styles['collapse-header']}>
              <IconSvg type="arrow-down" size={16} className="collapse-header-arrow" />
              映射信息
            </div>
          }
          key="3"
        >
          <MapSettingTable batchObj={jobDetail as any} isReadOnly={true} />
        </Panel>
      </Collapse>
    </div>
  );
};

export default TaskConfig;
