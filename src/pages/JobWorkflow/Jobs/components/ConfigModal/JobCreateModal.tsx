/**
 * 新建工作流弹窗
 * 仅在没有工作流的情况下出现
 */
import {createJob, IJob} from '@api/job';
import {ITemplate, queryTemplateList} from '@api/template';
import {WorkspaceContext} from '@pages/index';
import {JobModalTypeEnum, JSON_FORMAT} from '@pages/JobWorkflow/constants';
import {RULE} from '@utils/regs';
import urls from '@utils/urls';
import {Button, Form, Input, Modal, Select, toast, Upload} from 'acud';
import {OutlinedButtonUpload} from 'acud-icon';
import {useMemoizedFn} from 'ahooks';
import React, {useContext, useEffect, useRef, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {IJobModalProps} from '.';
import styles from './index.module.less';

const JobCreateModal: React.FC<IJobModalProps> = (props: IJobModalProps) => {
  const {modalType, onSubmit} = props;
  const {workspaceId} = useContext(WorkspaceContext);
  const [isVisible, setIsVisible] = useState(false);
  const navigate = useNavigate();
  const [form] = Form.useForm();

  const uploadObjRef = useRef<IJob>({});
  // 查询字段
  const [templateArr, setTemplateArr] = useState<ITemplate[]>([]);
  const [loading, setLoading] = useState(false);
  // 文件上传字段 控制文件上传后显示文件名和禁用按钮
  const [fileJson, setFileJson] = useState<{
    name?: string;
    json?: string;
    disabled: boolean;
  } | null>(null);

  // 提交表单
  const handleOk = useMemoizedFn(async () => {
    setLoading(true);
    try {
      const obj = await form.validateFields();
      const {result, success} = await createJob({...uploadObjRef.current, ...obj}, workspaceId);
      if (success) {
        // 导航到编辑页面
        navigate(`${urls.jobDetail}?jobId=${result}&edit=true`);
      }
    } catch (error) {
      console.error(error);
    }
    setLoading(false);
  });
  // 关闭弹窗
  const handleCancel = useMemoizedFn(() => {
    onSubmit(false);
  });

  // 查询模板列表
  const queryList = async () => {
    const {result} = await queryTemplateList(workspaceId);
    setTemplateArr(result.result);
  };

  // 初始化表单
  const initFn = useMemoizedFn(() => {
    //  JSON 类型需要使用模板列表
    modalType === JobModalTypeEnum.JSON && queryList();
    form.resetFields();
    setFileJson(null);
    setLoading(false);
  });

  useEffect(() => {
    // 打开弹窗 初始化表单
    initFn();
    setIsVisible(true);
  }, []);

  // 上传文件前检查
  const beforeUpload = useMemoizedFn((file: File) => {
    const reader = new FileReader();

    reader.addEventListener(
      'load',
      () => {
        const json = reader.result!.toString();
        let code = '';
        try {
          const obj: IJob = JSON.parse(json); // 尝试解析 JSON
          uploadObjRef.current = obj;
          code = JSON.stringify(JSON.parse(obj.code), null, JSON_FORMAT);
        } catch {
          toast.error({
            message: '文件格式错误，请检查json文件',
            duration: 5
          });
          return;
        }

        // 然后这将显示一个文本文件
        setFileJson({
          name: file.name,
          json: code,
          disabled: false
        });

        form.setFieldValue('code', code);
        form.validateFields(['code']);
      },
      false
    );
    reader.readAsText(file);

    return Upload.LIST_IGNORE;
  });

  // 切换模板
  const changeTemplate = useMemoizedFn((value: string) => {
    setFileJson({disabled: !!value});
    uploadObjRef.current = {};
  });

  return (
    <Modal
      closable={true}
      width={500}
      title={modalType === JobModalTypeEnum.EMPTY ? '创建空白工作流' : '导入工作流'}
      visible={isVisible}
      onOk={handleOk}
      confirmLoading={loading}
      onCancel={handleCancel}
      className={styles['job-create-modal']}
    >
      <Form name="basic" layout="vertical" labelAlign="left" form={form} inputMaxWidth="500px">
        <Form.Item
          label="工作流名称"
          name="name"
          rules={[
            {required: true, message: '请输入工作流名称'},
            {pattern: RULE.workflowName, message: RULE.workflowNameText}
          ]}
        >
          <Input placeholder={RULE.workflowNameText} allowClear forbidIfLimit={true} limitLength={256} />
        </Form.Item>

        <Form.Item label="描述" name="description">
          <Input.TextArea
            autoSize={{minRows: 3, maxRows: 6}}
            forbidIfLimit={true}
            limitLength={500}
            placeholder="请输入工作流描述"
            allowClear
          />
        </Form.Item>

        {modalType === JobModalTypeEnum.JSON && (
          <div className={styles['template-upload']}>
            <Form.Item
              className={styles['template-select']}
              label="选择模板"
              name="code"
              rules={[
                {
                  required: true,
                  message: '请选择模板或上传文件'
                }
              ]}
              extra="可以从模板中导入，也可以自定义上传，支持json格式"
            >
              <Select
                style={{width: '100%'}}
                allowClear
                placeholder="请选择模板"
                options={[...templateArr, {templateName: fileJson?.name, jobTemplate: fileJson?.json}]
                  .filter((item) => item.templateName!)
                  .map((item) => {
                    return {
                      label: String(item.templateName),
                      value: item.jobTemplate!
                    };
                  })}
                onChange={changeTemplate}
              ></Select>
            </Form.Item>

            <Form.Item label=" " className={styles['upload-btn']}>
              <Upload accept="application/json" desPosition="right" beforeUpload={beforeUpload}>
                <Button
                  className={styles['upload-btn']}
                  disabled={fileJson?.disabled}
                  icon={<OutlinedButtonUpload />}
                >
                  上传文件
                </Button>
              </Upload>
            </Form.Item>
          </div>
        )}
      </Form>
    </Modal>
  );
};

export default JobCreateModal;
