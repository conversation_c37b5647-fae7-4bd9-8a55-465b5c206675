import {Order} from '@api/common';
import {BatchSinkConfig, BatchSourceConfig, SourceMapping} from './batch';
import {Privilege} from '@api/permission/type';

/** File 类型任务数据源类型 */
export enum FileSourceType {
  FTP = 'FTP',
  SFTP = 'SFTP'
}

/** File 类型任务目的端类型 */
export enum FileSinkType {
  Catalog = 'Catalog'
}

/** 任务类型 */
export enum JobType {
  File = 'file',
  Batch = 'batch',
  CDC = 'cdc'
}

/** File 类型任务运行类型 */
export enum FileJobRunType {
  /** 周期性调度 - 工作流例行执行 */
  WorkflowSchedule = 'workflowSchedule',

  /** 工作流单次执行 */
  WorkflowOnce = 'workflowOnce',

  /** 手动触发 - 单次执行 */
  Once = 'once',

  /** 补数据执行 （该类型只存在于接口文档中，UE 稿中暂无） */
  Rewind = 'rewind',

  /** 重跑 */
  Rerun = 'rerun'
}

// JobConfig 定义（当前仅支持 File 类型）

/** file 类型任务的配置项 */
export interface FileJobConfig {
  /** 数据源类型 */
  sourceType: FileSourceType;

  /** 目的端类型 */
  sinkType: FileSinkType;

  /** 数据源连接 ID */
  sourceConnectionId: string;

  /** 目的地路径 */
  sinkVolume: string;

  /** 文件路径 */
  sourceFilePath: string;

  /** 正则路径过滤 */
  sourceFileFilterPattern: string;

  /** 文件格式过滤 */
  sourceFileNameExtension: string;

  /** 并行度，默认为 1，最大值为 6 */
  parallelism: number;
}

/** 当前支持的任务配置类型（目前仅支持 File） */
export type JobConfig = FileJobConfig;

/** 创建任务请求参数 */
export interface JobDetail {
  /** 创建时间，从请求体中传递 */
  createTime?: string;

  /** 任务名称 */
  name: string;

  /** 资源配置 */
  compute: {name: string; computeId: string};

  /** 任务类型（当前仅支持 file） */
  type?: JobType;

  /** 任务描述（可选） */
  description?: string;

  /** 任务详细配置 */
  config: JobConfig;

  /** 创建人 */
  creatorName?: string;

  /** 运行状态 */
  status: JobConfigStatus;

  /** 任务配置状态 */
  configStatus: JobConfigStatus;

  /** 结构化 - 来源配置 */
  sourceConfig?: BatchSourceConfig;

  /** 结构化 - 目的配置 */
  sinkConfig?: BatchSinkConfig;

  /** 结构化 - 映射配置 */
  mappingConfig?: SourceMapping;

  /** 权限 */
  privileges?: string[];
}

/** [V2] File 任务源端配置类型 */
export interface FileSourceConfig {
  /** 数据源类型 */
  sourceType: FileSourceType;

  /** 数据源连接 ID */
  sourceConnectionId: string;

  /** 文件路径 */
  sourceFilePath: string;

  /** 正则路径过滤 */
  sourceFileFilterPattern: string;

  /** 文件格式过滤 */
  sourceFileNameExtension: string;

  /** 文件更新过滤时间 - 开始时间 */
  sourceFileFilterModMin: string;

  /** 文件更新过滤时间 - 结束时间 */
  sourceFileFilterModMax: string;

  /** 并行度，默认为 1，最大值为 6 */
  parallelism: number;
}

/** [V2] File 任务目的端配置类型 */
export interface FileSinkConfig {
  /** 目的端类型 */
  sinkType: FileSinkType;

  /** 目的地路径 */
  sinkVolume: string;
}

/** [V2] 任务详情 Base 类型，用于后续创建任务、更新任务、展示任务详情时共用字段 */
export interface JobDetailBase {
  /** 任务名称 */
  name: string;

  /** 资源配置 */
  compute: {name: string; computeId: string};

  /** 任务类型 */
  type: JobType;

  /** 任务描述 */
  description?: string;

  /** 任务源端配置，暂时只考虑 File 类型任务 */
  sourceConfig: FileSourceConfig;

  /** 任务目的端配置，暂时只考虑 File 类型任务 */
  sinkConfig: FileSinkConfig;

  /** 任务映射配置，File 类型任务暂不支持 */
  mappingConfig?: null;
}

/** [V2] 任务创建请求参数 */
export interface JobCreateReq extends JobDetailBase {}

/** [V2] 任务更新请求参数 */
export interface JobUpdateReq extends JobDetailBase {
  /**
   * 是否针对已发布版本进行更新
   * 如果为 true，则更新任务的发布版本
   * 如果为 false，则更新任务的草稿版本
   */
  isPublished: boolean;
}

/** [V2] 任务详情请求参数 */
export interface JobDetailReq {
  /** 是否获取已发布版本信息，默认为 true */
  isPublished?: boolean;
}

/** [V2] 任务详情响应结构 */
export interface JobDetailRes extends JobDetailBase {
  /** 工作空间 ID */
  workspaceId: string;

  /** 创建人 ID */
  creatorId: string;

  /** 创建人名称 */
  creatorName: string;

  /** 创建时间 */
  createTime: string;

  /** 任务 ID */
  jobId: string;

  /** 任务配置状态 */
  status: JobConfigStatus;

  privileges: Privilege[];
}

/** 排序字段（目前仅支持按 createTime） */
export enum JobOrderBy {
  CreateTime = 'createTime'
}

/** 任务运行状态 按照 JobRunStatus 理解 */
export enum JobStatus {
  Ready = 'READY',
  Published = 'PUBLISHED',
  Running = 'RUNNING',
  Success = 'SUCCESS',
  Failed = 'FAILED',
  Suspending = 'STOPPING',
  Suspend = 'STOPPED'
}

/** [V2] 任务配置状态 */
export enum JobConfigStatus {
  /** 草稿 */
  Draft = 'DRAFT',

  /** 前置检查中 */
  PreCheck = 'PRECHECK',

  /** 检查通过 */
  CheckPass = 'CHECKPASS',

  /** 检查不通过 */
  CheckFail = 'CHECKFAILED',

  /** 已经发布 */
  Published = 'PUBLISHED',

  /** 正在更新 */
  Updating = 'UPDATING'
}

/** 查询任务列表请求参数 */
export interface QueryJobListReq {
  /** 数据源类型（可选） */
  sourceType?: FileSourceType | undefined;

  /** 任务名正则匹配（可选） */
  namePattern?: string;

  /** 页码（默认 1） */
  pageNo?: number;

  /** 每页数量（默认 10） */
  pageSize?: number;

  /** 排序字段（目前仅支持 createTime） */
  orderBy?: JobOrderBy;

  /** 排序方式（asc/desc） */
  order?: Order;

  type: JobType;
}

/** 单次运行信息 */
export interface RunStats {
  /** 运行 ID */
  runId: string;

  /** 运行时间 */
  startTime: string;

  /** 运行状态 */
  status: JobStatus;
}

/** 单个 Job 数据结构（file batch 类型） */
export interface Job {
  /** 任务 ID */
  jobId: string;

  /** 任务名称 */
  name: string;

  /** 数据源类型 */
  sourceType: FileSourceType;

  /** 源连接 ID */
  sourceConnectionId: string;

  /** 目的地路径 */
  sinkVolume: string;

  /** 最近 5 次运行记录 */
  latestRunStats: RunStats[];

  /** 创建人 ID */
  creatorName: string;

  /** 创建时间 */
  createTime: string;

  /** 状态 */
  status?: JobConfigStatus;

  /** 权限 */
  privileges?: string[];
}

/** 查询任务列表响应结构 */
export interface QueryJobListRes {
  /** 页码 */
  pageNo: number;

  /** 每页数量 */
  pageSize: number;

  /** 总记录数 */
  totalCount: number;

  /** 任务列表 */
  jobs: Job[];
}

/** 任务状态信息 */
export interface JobStatusInfo {
  /** 任务 ID */
  jobId: string;

  /** 当前任务状态 */
  status: JobStatus;
}

/** 单个任务名称信息 */
export interface JobInfo {
  /** 任务 ID */
  jobId: string;

  /** 任务名称 */
  jobName: string;
}

/** 获取任务名称列表的响应结构 */
export interface JobNameListRes {
  /** 任务名称列表 */
  jobNames: JobInfo[];
}

/** 排序字段：运行时间或结束时间 */
export enum ExecutionOrderBy {
  ExecuteTime = 'startTime',
  EndTime = 'endTime'
}

/** 实例运行状态 */
export enum InstanceStatus {
  Ready = 'READY',
  Running = 'RUNNING',
  Success = 'SUCCESS',
  Failed = 'FAILED',
  Stop = 'STOPPED',
  Stopping = 'STOPPING'
}

/** 查询任务运行记录请求参数 */
export interface ListExecutionsReq {
  /** 运行状态（可选） */
  status?: InstanceStatus;

  /** 执行类型（可选） */
  triggerType?: JobTriggerType | FileJobRunType;

  /** 页码（默认 1） */
  pageNo?: number;

  /** 每页数量（默认 10） */
  pageSize?: number;

  /** 排序字段（executeTime 或 endTime） */
  orderBy?: ExecutionOrderBy;

  /** 排序方式（asc 或 desc） */
  order?: Order;
}

/** 单条任务运行记录 */
export interface ExecutionInfo {
  /** 运行 ID */
  runId: string;

  /** 业务时间 */
  scheduleTime: string;

  /** 运行状态 */
  status: InstanceStatus;

  /** 运行类型 */
  triggerType: FileJobRunType;

  /** 运行时间 */
  executedTime: string;

  /** 结束时间 */
  endTime: string;

  /** 运行时长（单位：秒或毫秒，视后端定义） */
  duration: number;
}

/** 查询任务运行记录响应结构 */
export interface ListExecutionRes {
  /** 运行记录列表 */
  executions: ExecutionInfo[];
  totalCount: number;
}

/**
 * 批量操作类型
 */
export enum BatchOperateType {
  Start = 'start',
  Stop = 'stop',
  Delete = 'delete'
}

// ========== 请求参数 ==========

/** 查询任务执行日志的请求参数 */
export interface QueryExecutionLogReq {
  /** 每页日志行数（必须） */
  pageSize: number;

  /** 页码（可选，默认返回最新一页） */
  pageNo?: number;
}

// ========== 响应结构 ==========

/** 查询任务执行日志的响应结构 */
export interface QueryExecutionLogRes {
  /** 日志总页数 */
  totalLogPageCount: number;

  /** 任务是否已完成 */
  taskFinished: boolean;

  /** 日志内容（纯文本） */
  logContent: string;

  /** 页面大小 */
  pageSize: number;

  /** 当前页码 */
  pageNo: number;
}

// 任务触发类型
export enum JobTriggerType {
  WorkflowSchedule = 'workflowSchedule',
  WorkflowOnce = 'workflowOnce',
  Once = 'once',
  Rewind = 'rewind',
  Rerun = 'rerun'
}

/** 任务触发类型映射 */
export const JobTriggerTypeMap = {
  [JobTriggerType.WorkflowSchedule]: '例行执行',
  [JobTriggerType.WorkflowOnce]: '工作流单次执行',
  [JobTriggerType.Once]: '单次执行',
  [JobTriggerType.Rewind]: '补数据执行',
  [JobTriggerType.Rerun]: '重跑'
};
/** 运行时参数 */
export interface RuntimeArgs {
  triggerType?: JobTriggerType | FileJobRunType;
  scheduleTime?: string;
  workflowJobName?: string;
  workflowJobId?: string;
}

// 前置检查结果
export interface PreCheckResult {
  result: PreCheckResultItem[];
  success: boolean;
  canForcePass: boolean;
}

// 前置检查结果项
export interface PreCheckResultItem {
  name: string;
  status: string;
  message: string;
  subscription: string;
  subItems: PreCheckResultSubItem[];
}

// 前置检查子项
export interface PreCheckResultSubItem {
  name: string;
  status: string;
  message: string;
  subscription: string;
}

// 任务信息前端传递数据结构
export interface IJobItem {
  jobId: string;
  name: string;
  workspaceId?: string;
  status?: string;
}

/**
 * 操作函数参数
 * @param job 任务信息 传递数组 主要需要 jobId 和 name
 * @param workspaceId 工作空间 ID
 * @param callback 操作回调 （一般是刷新当前页面）
 * @param navigate 导航函数 （一般是跳转页面，静态方法 没有 navigate）
 */
export type OperateArgs = [IJobItem[], workspaceId: string, (() => void)?, ((v?: any) => void)?];
