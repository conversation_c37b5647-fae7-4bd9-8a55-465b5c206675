import {IJobInstance} from '@api/jobInstance';
import React from 'react';
import DescriptionList from '../../DescriptionList';
import {CronTypeEnum, JobScheduleStatusChinese, JobScheduleStatusEnum} from '@pages/JobWorkflow/constants';
import {dealCronToStr} from '@pages/JobWorkflow/tools';

const JobCronParams: React.FC<{
  jobInstance: IJobInstance;
}> = ({jobInstance}) => {
  const infoList = [
    {
      label: '调度状态',
      value: JobScheduleStatusChinese[jobInstance?.scheduleStatus || JobScheduleStatusEnum.OFF]
    },
    {
      label: '起始时间',
      value: jobInstance?.scheduleConf?.startTime
    },
    {
      label: '终止时间',
      value: jobInstance?.scheduleConf?.endTime
    },
    {
      label: '定时',
      value:
        jobInstance?.scheduleConf?.type === CronTypeEnum.OTHER
          ? jobInstance?.scheduleConf?.crontab
          : dealCronToStr(jobInstance?.scheduleConf)
    }
  ];

  return (
    <>
      <DescriptionList infoList={infoList} colon={false} />
    </>
  );
};

export default JobCronParams;
