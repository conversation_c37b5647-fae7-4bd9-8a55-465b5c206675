/**
 * 任务实例模板列表
 * 展示已创建的任务实例模版，支持编辑和删除
 * @author: zhao<PERSON><PERSON><EMAIL>
 */
import React, {
  useState,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useImperativeHandle,
  useRef
} from 'react';
import {Table, Button, Modal, Loading, Link} from 'acud';
import {Plus1} from '@baidu/xicon-react-bigdata';
import urls from '@utils/urls';
import {useNavigate} from 'react-router-dom';
import {TaskInstanceTemplate, getTaskInstanceTemplateList, deleteTaskInstanceTemplate} from '@api/Compute';
import useTable from '@hooks/useTable';
import {WorkspaceContext} from '@pages/index';
import styles from '../index.module.less';
import classNames from 'classnames/bind';
import useWorkspaceAuth from '@hooks/useWorkspaceAuth';
import {Privilege, ResourceType} from '@api/permission/type';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import {TooltipType} from '@components/AuthComponents/constants';
import PermissionModal, {PermissionModalRef} from '@components/Workspace/PermissionModal';
import {privilegeList} from '@pages/Compute/config';
const cx = classNames.bind(styles);

export interface RefreshableListRef {
  refresh: () => void;
}
const TaskTemplateList = (_, ref) => {
  const navigate = useNavigate();
  const authList = useWorkspaceAuth([Privilege.EtlJobTemplateCreate]);
  const canCreate = authList[Privilege.EtlJobTemplateCreate];
  const {workspaceId} = useContext(WorkspaceContext);
  const permissionModalRef = useRef<PermissionModalRef>(null);
  const columns: any = [
    {
      title: '实例模板名称',
      key: 'name',
      dataIndex: 'name',
      width: 150,
      ellipsis: {
        showTitle: false
      }
    },
    {
      title: '实例类型',
      key: 'engine',
      dataIndex: 'engine',
      width: 80
    },
    {
      title: '镜像版本',
      key: 'mirrorVersion',
      dataIndex: 'mirrorVersion',
      width: 80
    },
    {
      title: '节点规格',
      key: 'clusterType',
      dataIndex: 'clusterType',
      width: 140,
      ellipsis: {
        showTitle: false
      },
      render: (_, record) => `${record.clusterType}（${record.nodeCnt} 个节点）`
    },
    {
      title: '创建时间',
      key: 'createAt',
      dataIndex: 'createAt',
      width: 140,
      sorter: true
    },
    // {
    //   title: '付费方式',
    //   key: 'chargeType',
    //   dataIndex: 'chargeType',
    //   width: 80,
    //   render: (value: string) => {
    //     return PAY_TYPE.getTextFromValue(value);
    //   }
    // },
    {
      title: '操作',
      key: 'operation',
      width: 100,
      fixed: 'right',
      render: (_, record) => {
        const {privileges = []} = record;
        const canManage = privileges.includes(Privilege.Manage);
        return (
          <div className="whitespace-nowrap">
            <AuthComponents tooltipType={TooltipType.Function} isAuth={canManage}>
              <Link
                onClick={() => {
                  handleEdit(record.id);
                }}
              >
                编辑
              </Link>
            </AuthComponents>
            <AuthComponents tooltipType={TooltipType.Function} isAuth={canManage}>
              <Link className="ml-4" onClick={() => handleDelete(record.id)}>
                删除
              </Link>
            </AuthComponents>
            <AuthComponents tooltipType={TooltipType.Function} isAuth={canManage}>
              <Link
                className="ml-4"
                onClick={() => {
                  permissionModalRef.current?.open({
                    resourceType: ResourceType.EtlJobTemplate,
                    resourceId: record.id,
                    resourceName: record.name,
                    privilegeList
                  });
                }}
              >
                权限管理
              </Link>
            </AuthComponents>
          </div>
        );
      }
    }
  ];
  const {
    dataSource,
    tableProps,
    loadTableData: getTaskTemplatelist,
    loading
  } = useTable<TaskInstanceTemplate>({
    getTableApi: getTaskInstanceTemplateList,
    listKey: 'templates',
    refreshDeps: [workspaceId],
    extraParams: {
      workspaceId
    }
  });
  useImperativeHandle(ref, () => ({
    refresh: getTaskTemplatelist
  }));
  const showBlankSpace = useMemo(() => dataSource.length === 0, [dataSource.length]);
  const handleCreate = useCallback(() => {
    navigate(`${urls.computeCreateTaskInstance}?workspaceId=${workspaceId}`);
  }, [workspaceId, navigate]);
  const handleEdit = (id) => {
    navigate(`${urls.computeCreateTaskInstance}?workspaceId=${workspaceId}&isEdit=true&templateId=${id}`);
  };
  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '提示',
      content: '确定要删除该模板吗？',
      onOk: async () => {
        try {
          await deleteTaskInstanceTemplate({workspaceId, templateId: id});
          getTaskTemplatelist();
        } catch (error) {
          console.error('删除失败', error);
        }
      }
    });
  };
  // 渲染空白状态
  const blankSpace = (
    <div className={cx('blank-space', 'h-full')}>
      <div className={cx('blank-title')}>创建任务实例模版</div>
      <div className={cx('blank-desc')}>创建常驻实例用于运行工作流任务</div>
      <AuthComponents tooltipType={TooltipType.Function} isAuth={canCreate}>
        <Button
          className={cx('blank-btn')}
          type="primary"
          icon={<Plus1 className="w-4 h-4" />}
          onClick={handleCreate}
        >
          立即创建
        </Button>
      </AuthComponents>
      <div className={cx('blank-img')}></div>
    </div>
  );

  if (loading && dataSource.length === 0) {
    return <Loading loading={loading} />;
  }

  return (
    <>
      {showBlankSpace ? (
        blankSpace
      ) : (
        <>
          <Table className={cx('compute-table')} columns={columns} rowKey="computeId" {...tableProps} />
          <PermissionModal ref={permissionModalRef} workspaceId={workspaceId} />
        </>
      )}
    </>
  );
};
const RefTaskTemplateList = React.forwardRef<RefreshableListRef>(TaskTemplateList);
export default RefTaskTemplateList;
