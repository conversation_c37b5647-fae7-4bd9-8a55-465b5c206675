/**
 * MetaActionBar组件 - Panel的顶部操作栏
 * <AUTHOR>
 */

import React, {useMemo} from 'react';
import {Button, Dropdown, Menu, Space, Tooltip} from 'acud';

import IconSvg from '@components/IconSvg';
import styles from './MetaAction.module.less';
import {CatalogType} from '@api/metaRequest';

import AuthButton from '@components/AuthComponents/AuthButton';
import AuthMenuItem from '@components/AuthComponents/AuthMenuItem';
import {Privilege} from '@api/permission/type';
import {isBuildInCatalog} from '../helper';

type IAuthMenuItem = {
  key: string; // 唯一 key
  label: string; // 下拉项文本
  authName: string; // 权限名称
};

interface IActionBase {
  catalog: CatalogType.SYSTEM | string; // 当 catalog 为 内置的 system 时，禁用相关操作
  icon: React.ReactNode; // 标题左侧图标
  title: string; // 标题
  dropdownMenu: IAuthMenuItem[]; // 下拉操作列
  onDropdownClick: (key: string) => void; // 下拉操作列点击事件
  createMenu?: IAuthMenuItem[]; // 创建按钮下拉列
  createDisabled?: boolean; // 是否禁用创建按钮
  createDisabledTooltip?: string; // 创建按钮禁用提示文本
  authList?: string[]; // 权限列表
  createAuthName?: Privilege; // 创建按钮权限名称
  otherButton?: React.ReactNode; // 其他按钮
}

interface IAvtionNoCreate extends IActionBase {
  hiddenCreate: true; // 无新建按钮（可选）
  createText?: any; // 确保 createText 不存在
  createIcon?: React.ReactNode; // 新建按钮图标
  onCreateClick?: any; // 确保 onCreateClick 不存在
}

interface IActionHasCreate extends IActionBase {
  hiddenCreate?: false; // 有新建按钮（可选）
  createText: string; // 新建按钮文本
  createIcon?: React.ReactNode; // 新建按钮图标
  onCreateClick: (key?: string) => void; // 新建按钮点击事件
}

type IMetaActionBar = IActionHasCreate | IAvtionNoCreate;

// 权限：下拉按钮 list 渲染
const AuthMenuEl = (props: {
  menu: IAuthMenuItem[];
  onClick: (key: string) => void;
  isDisabled: boolean;
  authList: string[];
}) => {
  const {menu = [], onClick, isDisabled, authList = []} = props;

  const onClickMenuItem = (e: any) => {
    const target = menu.find((item) => item.key === e.key);
    if (!target || isDisabled || !authList.length || !authList.includes(target.authName)) {
      return;
    }
    onClick(e.key);
  };

  return (
    <Menu onClick={onClickMenuItem}>
      {menu.map((item: IAuthMenuItem) => (
        <AuthMenuItem
          key={item.key}
          isAuth={authList.length && authList.includes(item.authName)}
          disabled={isDisabled}
        >
          {item.label}
        </AuthMenuItem>
      ))}
    </Menu>
  );
};

const MetaActionBar = (props: IMetaActionBar) => {
  const {
    catalog,
    icon,
    title,
    dropdownMenu,
    onDropdownClick,
    hiddenCreate,
    createText,
    createMenu,
    onCreateClick,
    createIcon,
    createDisabled,
    createDisabledTooltip,
    authList = [],
    createAuthName,
    otherButton
  } = props;

  // 内置的 system 和 EDAPDataLake 时，禁用相关操作
  const isDisabled = isBuildInCatalog(catalog as CatalogType);

  const createButton = useMemo(() => {
    const button = (
      <AuthButton
        disabled={isDisabled || createDisabled}
        isAuth={authList.length && authList.includes(createAuthName)}
        type="primary"
        icon={createIcon}
        onClick={() => onCreateClick()}
      >
        {createText}
      </AuthButton>
    );
    return createDisabled && createDisabledTooltip ? (
      <Tooltip title={createDisabledTooltip} placement="topRight">
        <Button icon={createIcon} disabled>
          {createText}
        </Button>
      </Tooltip>
    ) : (
      button
    );
  }, [
    createDisabled,
    createDisabledTooltip,
    createIcon,
    createText,
    authList,
    isDisabled,
    onCreateClick,
    createAuthName
  ]);

  // more图标 编辑按钮下拉列
  const editDomList = useMemo(
    () => AuthMenuEl({menu: dropdownMenu, onClick: onDropdownClick, isDisabled, authList}),
    [dropdownMenu, onDropdownClick, isDisabled, authList]
  );

  // 创建按钮下拉列
  const createDomList = useMemo(
    () => AuthMenuEl({menu: createMenu, onClick: onCreateClick, isDisabled, authList}),
    [createMenu, onCreateClick, isDisabled, authList]
  );

  // 立即创建, 当所有下拉菜单项都被禁用时，禁用按钮
  const isCreateButtonDisabled = useMemo(() => {
    if (!createMenu?.length) {
      return false;
    }
    // 检查是否所有菜单项都被禁用
    return createMenu.every((item) => {
      // 菜单项被禁用的条件：
      // 1.isDisabled 为 true (内置catalog禁用)
      // 2.全部创建权限都没有
      const hasAuth = authList.length && authList.includes(item.authName);
      return isDisabled || !hasAuth;
    });
  }, [createMenu, isDisabled, authList]);

  return (
    <div className={styles['meta-action']}>
      <div className={styles['meta-action-title']}>
        <span>{icon}</span>
        {title}
      </div>
      <div className={styles['meta-action-button-group']}>
        {dropdownMenu?.length ? (
          <Dropdown overlay={editDomList}>
            <Space>
              <Button className={styles['meta-action-button']}>
                <IconSvg type="more" size={16} />
              </Button>
            </Space>
          </Dropdown>
        ) : null}
        {hiddenCreate ? null : createMenu?.length ? (
          <div>
            <Dropdown overlay={createDomList}>
              <Space>
                <Button type="primary" icon={createIcon} disabled={isCreateButtonDisabled}>
                  立即创建
                </Button>
              </Space>
            </Dropdown>
          </div>
        ) : (
          createButton
        )}
        {otherButton}
      </div>
    </div>
  );
};

export default React.memo(MetaActionBar);
