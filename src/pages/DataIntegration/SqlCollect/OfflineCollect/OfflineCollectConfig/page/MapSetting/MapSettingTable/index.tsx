/**
 * 连线配置
 * 原始表 使用 前缀+名称 实现 id  => JsPlumbIdEnum.SOURCE + item.name
 * 目标表 添加 class id 字段，因为目标表名称有可能变化，所以使用 id 字段来标识目标表 id 也是 type + 名称
 *    新添加的目标表 使用 前缀 + 索引实现 id，避免编辑导致错误的连线
 */
import {
  BatchObj,
  BatchTableColumns,
  getDatasourceTableColumns,
  Partitioning,
  SourceMapping
} from '@api/integration/batch';
import {getTableDetail} from '@api/metaRequest';
import {BrowserJsPlumbInstance, Connection, newInstance} from '@jsplumb/browser-ui';
import {
  JsPlumbIdEnum,
  PartitioningFunctionEnum,
  SourceConfigEnum
} from '@pages/DataIntegration/SqlCollect/constants';
import {Button, Space, toast, Tooltip} from 'acud';
import {useMemoizedFn} from 'ahooks';
import React, {useContext, useEffect, useMemo, useRef, useState} from 'react';
import CardTwoContent from '../../../components/CardTwoContent';
import styles from './index.module.less';
import PartitioningTable from './PartitioningTable';
import SourceTable from './SourceTable';
import SqlEdit from './SqlEdit';
import TargetTable from './TargetTable';
import {dealMetaDataTable, dealTargetTable, targetColumnsDealId, typeMapFn} from '../utils';
import {WorkspaceContext} from '@pages/index';
import IconSvg from '@components/IconSvg';
import {Ellipsis} from '@baidu/bce-react-toolkit';

const enum MapTypeEnum {
  SAME_NAME = '同名映射',
  SAME_ROW = '同行映射',
  CANCEL_MAP = '取消映射'
}
/**
 * 映射配置
 * @param batchObj 批量对象
 * @param isReadOnly 是否只读 默认true
 * @param onChange 修改对象
 * @returns
 */

export interface MapSettingTableProps {
  batchObj: BatchObj;
  isReadOnly?: boolean;
  onChange?: (key: string[], value: any) => void;
}
/**
 * 表格映射
 * @param batchObj 结构化对象
 * @param isReadOnly 是否只读 默认只读 不可操作
 * @returns
 */
const MapSettingTable: React.FC<MapSettingTableProps> = ({batchObj, isReadOnly = true, onChange}) => {
  /** 源端数据 */
  const [sourceData, setSourceData] = useState<BatchTableColumns[]>([]);
  /** 目标端数据 初始化数据 */
  const [targetData, setTargetData] = useState<BatchTableColumns[]>([]);
  const [sinkPartitions, setSinkPartitions] = useState<Partitioning[]>([]);
  /** 是否存在映射 */
  const [mappingArr, setMappingArr] = useState<SourceMapping[]>([]);
  /** 映射数据 */
  const containerRef = useRef<HTMLDivElement>(null);
  /** jsPlumb实例 */
  const jsPlumbInstance = useRef<BrowserJsPlumbInstance>(null);
  /** 连线数据 */
  const [lineDataMap, setLineDataMap] = useState<Map<string, Set<string>>>(new Map());

  const {workspaceId} = useContext(WorkspaceContext);

  // 通过元数据 或者已有表 获取目标表
  const getTargetTableFromPath = useMemoizedFn(async (sinkPath: string) => {
    const res = await getTableDetail(workspaceId, sinkPath);
    const sinkFields = dealMetaDataTable(res.result);
    setTargetData(sinkFields);
    onChange?.([SourceConfigEnum.MappingConfig, 'sinkFields'], sinkFields);
    setTimeout(() => {
      initTarget();
      initLine();
    }, 100);
  });

  /** 获取源端表数据 */
  const getSourceTableData = useMemoizedFn(async () => {
    let sourceData = [];
    if (batchObj.sourceTable && batchObj.sourceTable.length > 0) {
      sourceData = batchObj.sourceTable;
    } else {
      const res = await getDatasourceTableColumns({
        environment: {
          workspaceId: batchObj?.workspaceId,
          computeId: batchObj?.compute?.computeId
        },
        datasourceInfo: {
          connectionId: batchObj?.sourceConfig?.sourceConnectionId,
          database: batchObj?.sourceConfig?.sourceDatabase,
          table: batchObj?.sourceConfig?.sourceTable
        }
      });
      sourceData = res.result.columns;
    }
    setSourceData(sourceData);
    getTargetTableData();
  });

  /** 获取目标端表数据 */
  const getTargetTableData = useMemoizedFn(async () => {
    // 查看是否已编辑过目标表
    const sinkFields = batchObj?.[SourceConfigEnum.MappingConfig]?.sinkFields;
    setTargetData(targetColumnsDealId(sinkFields));

    setTimeout(() => {
      initSource();
      initTarget();
      initLine();
    }, 100);
  });

  /** batchObj变化 初始化 源端目的端 */
  useEffect(() => {
    if (batchObj) {
      setTargetData(batchObj?.[SourceConfigEnum.MappingConfig]?.sinkFields || []);
      setSinkPartitions(
        batchObj?.[SourceConfigEnum.MappingConfig]?.sinkPartitions?.map((item, index) => {
          return {
            ...item,
            id: 'id_' + index
          };
        }) || []
      );
      setMappingArr(batchObj?.[SourceConfigEnum.MappingConfig]?.mapping || []);
      getSourceTableData();
    }
  }, [batchObj, getSourceTableData]);

  /** 映射处理 计算连线 并设置 mapping 表单数据 */
  const dealMapResult = useMemoizedFn(() => {
    // 等待 dom 加载完成  计算连线数量
    setTimeout(() => {
      const mapping: SourceMapping[] = [];
      const lineDataMapTem = new Map<string, Set<string>>();
      jsPlumbInstance.current?.connections.forEach((item) => {
        const sourceId = item.source.id;
        let targetId = '';

        const sourceName = sourceId.replace(JsPlumbIdEnum.SOURCE, '');
        for (const className of item.target.classList) {
          if (className.startsWith(JsPlumbIdEnum.TARGET) && className !== JsPlumbIdEnum.TARGET) {
            targetId = className;
            break;
          }
        }

        const sourceItem = sourceData.find((item) => item.name === sourceName);
        const targetItem = targetData.find((item) => item.id === targetId);

        // 判断避免重复连线
        const isExist = lineDataMapTem.get(sourceId)?.has(targetId);
        if (sourceItem && targetItem && !isExist) {
          mapping.push?.({sourceColumn: sourceItem.name, sinkColumn: targetItem.name});
        }

        if (lineDataMapTem.has(sourceId)) {
          lineDataMapTem.get(sourceId)?.add(targetId);
        } else {
          lineDataMapTem.set(sourceId, new Set([targetId]));
        }
      });

      setLineDataMap(lineDataMapTem);
      setMappingArr(mapping);
      onChange?.([SourceConfigEnum.MappingConfig, 'mapping'], mapping);
    }, 100);
  });

  /** 点击映射 */
  const handleMap = useMemoizedFn((type: MapTypeEnum) => {
    jsPlumbInstance.current?.deleteEveryConnection();
    const num = Math.min(sourceData.length, targetData.length);
    const map = new Map<string, BatchTableColumns>();
    sourceData.forEach((item) => {
      map.set(item.name, item);
    });
    switch (type) {
      case MapTypeEnum.SAME_NAME:
        targetData?.forEach((item) => {
          const sourceItem = map.get(item.name);
          if (sourceItem) {
            jsPlumbInstance.current?.connect({
              source: document.getElementById(JsPlumbIdEnum.SOURCE + sourceItem.name),
              target: document.getElementsByClassName(item.id)[0],
              anchors: ['Right', 'Left']
            });
          }
        });
        break;
      case MapTypeEnum.SAME_ROW:
        for (let i = 0; i < num; i++) {
          jsPlumbInstance.current?.connect({
            source: document.getElementById(JsPlumbIdEnum.SOURCE + sourceData[i].name),
            target: document.getElementsByClassName(targetData[i].id)[0],
            anchors: ['Right', 'Left']
          });
        }
        break;
      case MapTypeEnum.CANCEL_MAP:
        break;
    }
    dealMapResult();
  });

  /** 判断连线是否存在 */
  const isExistLine = useMemoizedFn((sourceId: string, targetId: string) => {
    const isExist = lineDataMap.get(sourceId)?.has(targetId);
    if (isExist) {
      toast.error({
        message: '已经存在相同的连接，禁止重复连线',
        duration: 5
      });
      return false; // 阻止创建重复连接
    }
    dealMapResult();
    return true;
  });

  /** 初始化jsPlumb */
  const initJsPlumbFn = () => {
    const jsPlumb = newInstance({
      container: containerRef.current!,
      endpointStyle: {
        strokeWidth: 10,
        type: 'None'
      },
      hoverPaintStyle: {stroke: '#f56c6c', strokeWidth: 2}, // 连线 hover 样式

      connectionOverlays: [
        {
          type: 'Arrow',
          options: {
            stroke: '#f56c6c',
            location: 1,
            width: 6,
            length: 6,
            direction: 1,
            foldback: 0.8
          }
        }
      ]
    });

    jsPlumbInstance.current = jsPlumb;

    // 删除连线 重新计算映射
    jsPlumbInstance.current?.bind('beforeDetach', (info) => {
      console.log(info, 'info');
      dealMapResult();
      return true;
    });
    jsPlumbInstance.current?.bind('beforeDrop', (info) => {
      return isExistLine(info.sourceId, info.targetId);
    });
  };

  useEffect(() => {
    initJsPlumbFn();
  }, []);

  /** 初始化源端 */
  const initSource = useMemoizedFn(() => {
    if (!jsPlumbInstance.current) return;
    sourceData.forEach((item) => {
      const sourceId = JsPlumbIdEnum.SOURCE + item.name;
      const element = document.getElementById(sourceId);
      if (element && !isReadOnly) {
        jsPlumbInstance.current.manage(element, sourceId);
      }
    });
    jsPlumbInstance.current.addSourceSelector(`.${JsPlumbIdEnum.SOURCE}`, {
      anchor: 'Right'
    });
  });

  /** 初始化目标端 包括 初始化 和添加节点 */
  const initTarget = useMemoizedFn(() => {
    if (!jsPlumbInstance.current) return;
    targetData?.forEach((item) => {
      const targetId = item.id;
      const element = document.getElementsByClassName(targetId)[0];
      if (element && !isReadOnly) {
        jsPlumbInstance.current.manage(element, targetId);
      }
    });
    jsPlumbInstance.current.addTargetSelector(`.${JsPlumbIdEnum.TARGET}`, {
      anchor: 'Left'
    });
  });

  /** 初始化连线 */
  const initLine = useMemoizedFn(() => {
    // 删除所有连线
    jsPlumbInstance.current?.deleteEveryConnection();
    // 初始化目标端需要连线已有数据
    mappingArr?.forEach((item) => {
      const sourceId = document.getElementById(JsPlumbIdEnum.SOURCE + item.sourceColumn);
      const targetClass = document.getElementsByClassName(JsPlumbIdEnum.TARGET + item.sinkColumn)?.[0];

      if (sourceId && targetClass) {
        jsPlumbInstance.current?.connect({
          source: sourceId,
          target: targetClass,
          anchors: ['Right', 'Left']
        });
      }
    });
  });

  // 刷新目标表
  const refreshTargetTable = useMemoizedFn(() => {
    let sinkPath = '';
    // 如果是自动创建 并且 表已经创建
    if (batchObj?.[SourceConfigEnum.SinkConfig]?.isAutoCreated && batchObj.sinkFullName) {
      sinkPath = batchObj.sinkFullName;
    } else if (!batchObj?.[SourceConfigEnum.SinkConfig]?.isAutoCreated) {
      sinkPath = batchObj?.[SourceConfigEnum.SinkConfig]?.sinkPath;
    }
    getTargetTableFromPath(sinkPath);
  });

  const left = useMemoizedFn(() => {
    return (
      <>
        <div className={styles['content-title']}>
          <span className={styles['title']}>
            <Ellipsis tooltip={batchObj?.[SourceConfigEnum.SourceConfig]?.sourceTable}>
              {batchObj?.[SourceConfigEnum.SourceConfig]?.sourceTable}
            </Ellipsis>
          </span>
          {!isReadOnly && !batchObj?.[SourceConfigEnum.SinkConfig]?.isAutoCreated && (
            <Space>
              <Button onClick={() => handleMap(MapTypeEnum.SAME_NAME)} type="actiontext">
                同名映射
              </Button>
              <Button onClick={() => handleMap(MapTypeEnum.SAME_ROW)} type="actiontext">
                同行映射
              </Button>
              <Button onClick={() => handleMap(MapTypeEnum.CANCEL_MAP)} type="actiontext">
                取消映射
              </Button>
            </Space>
          )}
        </div>
        <SourceTable tableData={sourceData} isReadOnly={isReadOnly} />
        {/* 只读模式下  如果where 为空 不展示 */}
        {!(isReadOnly && !batchObj?.[SourceConfigEnum.MappingConfig]?.filter) && (
          <>
            <div style={{marginTop: 32}} className={styles['content-title']}>
              <Space>
                where语句
                <Tooltip
                  title={`在对源端数据进行过滤时，只需填写 WHERE 子句的条件部分，无需包含 WHERE 关键字本身。若已启用周期性配置，可利用系统参数变量来构建过滤条件。例如：'\${logicTime(yyyy-MM-dd HH:mm:ss,-1d)}' <= columnName AND columnName < '\${logicTime(yyyy-MM-dd HH:mm:ss)}'此示例中，\${logicTime} 为系统参数变量，用于动态指定日期范围，确保筛选出符合特定日期区间的数据`}
                >
                  <IconSvg type="info" fill="none" />
                </Tooltip>
              </Space>
            </div>

            <SqlEdit
              isReadOnly={isReadOnly}
              value={batchObj?.[SourceConfigEnum.MappingConfig]?.filter}
              onChange={(value) => {
                onChange?.([SourceConfigEnum.MappingConfig, 'filter'], value);
              }}
            />
          </>
        )}
      </>
    );
  });

  /** 目标端表名称 */
  const sinkTableName = useMemo(() => {
    if (!batchObj) {
      return '-';
    }
    return dealTargetTable(batchObj);
  }, [batchObj]);

  const handleChangeSinkPartitions = useMemoizedFn((value: Partitioning[]) => {
    onChange?.(
      [SourceConfigEnum.MappingConfig, 'sinkPartitions'],
      value?.map((item) => {
        if (item.function === PartitioningFunctionEnum.identity) {
          return {
            name: item.name,
            function: item.function
          };
        }
        return item;
      })
    );
  });

  /** 只读模式下  如果分区配置 和 目标端表 为空，则不显示分区配置 */
  const showPartitioning = useMemo(() => {
    // 目标端表为空 不显示分区配置
    if (targetData?.length === 0) {
      return false;
    }
    // 非自动创建 不显示分区配置
    if (!batchObj?.[SourceConfigEnum.SinkConfig]?.isAutoCreated) {
      return false;
    }
    // 已创建表 并且 分区为空 不显示分区配置
    if (batchObj?.sinkFullName && batchObj?.mappingConfig?.sinkPartitions?.length === 0) {
      return false;
    }
    if (isReadOnly && batchObj?.mappingConfig?.sinkPartitions?.length === 0) {
      return false;
    }
    return true;
  }, [batchObj, targetData, isReadOnly]);

  const targetTableReadOnly = useMemo(() => {
    if (isReadOnly) {
      return true;
    }
    if (batchObj?.sinkFullName) {
      return true;
    }
    if (!batchObj?.[SourceConfigEnum.SinkConfig]?.isAutoCreated) {
      return true;
    }
    return false;
  }, [batchObj, isReadOnly]);
  // 主键按钮是否只读
  const isPrimaryKeyReadOnly = useMemo(() => {
    if (isReadOnly) {
      return true;
    }
    if (!batchObj?.[SourceConfigEnum.SinkConfig]?.isAutoCreated) {
      return false;
    }

    return targetTableReadOnly;
  }, [batchObj, isReadOnly, targetTableReadOnly]);

  const right = useMemoizedFn(() => {
    return (
      <>
        <div className={styles['content-title']}>
          <span className={styles['title']}>
            <Ellipsis tooltip={sinkTableName}>{sinkTableName}</Ellipsis>
          </span>
          {/* 编辑模式 （ 已创建表 或这 选择已有表） */}
          {!isReadOnly &&
            (batchObj?.sinkFullName || !batchObj?.[SourceConfigEnum.SinkConfig]?.isAutoCreated) && (
              <Button type="actiontext" onClick={() => refreshTargetTable()}>
                刷新
              </Button>
            )}
        </div>
        <TargetTable
          sinkType={batchObj?.[SourceConfigEnum.SinkConfig]?.sinkType}
          tableData={targetData}
          isReadOnly={targetTableReadOnly}
          showDelete={!isReadOnly}
          isPrimaryKeyReadOnly={isPrimaryKeyReadOnly}
          deleteNode={(id) => {
            const element = document.getElementsByClassName(id)[0];
            if (element) {
              jsPlumbInstance.current?.deleteConnectionsForElement(element);
              jsPlumbInstance.current?.removeAllEndpoints(element);
              jsPlumbInstance.current?.unmanage(element);
            }
            jsPlumbInstance.current?.repaintEverything(); // 刷新整个画布
          }}
          onChangeList={(list) => {
            setTargetData(list);
            onChange?.([SourceConfigEnum.MappingConfig, 'sinkFields'], list);
            // 等待 dom 加载完成 需要初始化可连接点
            setTimeout(() => {
              initTarget();
              dealMapResult();
              jsPlumbInstance.current?.repaintEverything(); // 刷新整个画布
            }, 100);
          }}
        />

        {showPartitioning && (
          <>
            <div style={{marginTop: 32}} className={styles['content-title']}>
              目标端表分区
            </div>

            <PartitioningTable
              isReadOnly={isReadOnly || !!batchObj?.sinkFullName}
              sinkFields={targetData}
              value={sinkPartitions}
              onChange={handleChangeSinkPartitions}
            />
          </>
        )}
      </>
    );
  });

  // 监控屏幕大小变化
  useEffect(() => {
    const handleResize = () => {
      jsPlumbInstance.current?.repaintEverything(); // 刷// 刷新整个画布
    };
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <div className={styles['map-setting']} ref={containerRef}>
      <CardTwoContent
        left={left()}
        right={right()}
        title={['源端字段', '目标端字段']}
        paddingTop={14}
        showDivider={mappingArr?.length === 0}
      ></CardTwoContent>
    </div>
  );
};

export default MapSettingTable;
