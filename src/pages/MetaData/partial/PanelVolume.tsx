import {useCallback, useContext, useEffect, useMemo, useRef, useState} from 'react';
import {Form} from 'acud';
import {debounce} from 'lodash';

import DescriptionEdit from '@components/DescriptionEdit';
import InfoPanel from '@components/InfoPanel';
import IconSvg from '@components/IconSvg';

import * as http from '@api/metaRequest';
import {IUrlStateHandler} from '../index';

import MetaActionBar from '../components/MetaActionBar';
import MetaTabs from '../components/MetaTabs';
import ModalRemoveFun from '../components/ModalRemoveFun';
import ModalRename from '../components/ModalRename';

import VolumeFileInfoTable, {VolumeFileRefHandle} from '../components/VolumeFileInfoTable';
import {RULE} from '@utils/regs';
import {OutlinedButtonUpload} from 'acud-icon';
import UploadVolumeModal from '@components/UploadVolumeModal';
import CreateVolumeFolderModal from '@components/MetaCreateModal/CreateVolumeFolderModal';
import {CatalogType} from '@api/metaRequest';
import {MetaCnNameMap} from '../config';
import PermissionManage from '../../../components/PermissionManage';
import {ResourceType} from '@api/permission/type';

import {Privilege} from '@api/permission/type';
import {WorkspaceContext} from '@pages/index';
import {isBuildInCatalog} from '../helper';

enum PanelEnum {
  OVERVIEW = '1',
  DETAIL = '2',
  PERMISSION = '3'
}

const PanelVolume = (props: IUrlStateHandler) => {
  const {urlState, changeUrlFun, changeUrlFunReplace, handleTreeRefresh, userList} = props;
  const {catalog = '', schema = '', node = '', tab, path = ''} = urlState;
  const controllerRef = useRef(new AbortController());

  const initialPanes = useMemo(
    () => [
      {tab: '概览', key: PanelEnum.OVERVIEW, privilege: [Privilege.ReadVolume, Privilege.WriteVolume]},
      {tab: '详情', key: PanelEnum.DETAIL},
      ...(isBuildInCatalog(catalog as CatalogType)
        ? []
        : [{tab: '权限管理', key: PanelEnum.PERMISSION, privilege: [Privilege.Manage]}])
    ],
    [catalog]
  );

  const dropdownMenu = useMemo(
    () => [
      {
        key: 'rename',
        label: `重命名${MetaCnNameMap['Volume']}`,

        authName: Privilege.Manage
      },
      {
        key: 'remove',
        label: `删除${MetaCnNameMap['Volume']}`,

        authName: Privilege.Manage
      },
      {key: 'addFolder', label: '创建文件夹', authName: Privilege.WriteVolume}
    ],
    []
  );

  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);
  // volume 全名
  const fullName = `${catalog}.${schema}.${node}`;

  // 虚拟路径
  const volumePath = `/Volumes/${catalog}/${schema}/${node}/`;

  // 文件信息 Table 组件的 Ref
  const fileInfoTableRef = useRef<VolumeFileRefHandle>(null);

  // 详情 & 概览
  const [dataInfo, setDataInfo] = useState<http.IVolumeDetailRes>();

  const [form] = Form.useForm();

  // 详情字段
  const infoList = useMemo(() => {
    const info: any = dataInfo || {};
    return [
      {
        label: `${MetaCnNameMap['Catalog']}名称`,
        value: info.catalogName
      },
      {
        label: `${MetaCnNameMap['Schema']}名称`,
        value: info.schemaName
      },
      {
        label: `${MetaCnNameMap['Volume']}名称`,
        value: info.name
      },
      {
        label: `${MetaCnNameMap['Volume']}类型`,
        value: info.volumeType
      },
      {
        label: '存储路径',
        value: info.storageLocation
      },
      {
        label: '创建时间',
        value: info.createdAt
      },
      {
        label: '创建人',
        value: userList.find((item) => item.id === info.createdBy)?.name || info.createdBy
      },
      {
        label: '修改时间',
        value: info.updatedAt
      },
      {
        label: '最近修改人',
        value: userList.find((item) => item.id === info.updatedBy)?.name || info.updatedBy
      }
    ];
  }, [dataInfo, userList]);

  // 获取详情
  const getVolumeDetail = useCallback(async () => {
    const res = await http.getVolumeDetail(workspaceId, fullName);
    setDataInfo(res.result);
  }, [fullName, workspaceId]);

  // 初始化
  useEffect(() => {
    if (!(catalog && schema && node)) {
      return;
    }
    getVolumeDetail();
  }, [catalog, schema, node, getVolumeDetail]);

  // tab 切换
  const onTabChange = useCallback(
    (tabkey) => {
      changeUrlFun((preState) => ({...preState, tab: tabkey}));
    },
    [changeUrlFun]
  );

  useEffect(() => {
    const authList = dataInfo?.privileges || [];
    const previewPane = initialPanes.find((item) => item.key === PanelEnum.OVERVIEW);
    if (!previewPane) {
      return;
    }
    const privilaegs = previewPane.privilege || [];
    const tag = privilaegs.some((item: any) => authList?.includes(item));
    if (!tag) {
      return;
    }
    onTabChange(previewPane.key);
  }, [dataInfo?.privileges, onTabChange]);

  // 更新描述
  const onChangeDescript = useCallback(
    async (text: string) => {
      await http.patchVolume(workspaceId, fullName, {comment: text});
      getVolumeDetail();
    },
    [fullName, getVolumeDetail, workspaceId]
  );

  // 重命名 & 删除
  const [showRenameModal, setRenameModal] = useState<boolean>(false);
  const renameSuccessFun = useCallback(
    (formData: {name: string}) => {
      changeUrlFunReplace((preState) => ({...preState, node: formData.name}), true);
    },
    [changeUrlFunReplace]
  );
  const removeSuccessFun = useCallback(() => {
    changeUrlFunReplace((preState) => ({...preState, node: '', type: ''}), true);
  }, [changeUrlFunReplace]);
  const onDropdownClick = useCallback(
    (key) => {
      if (key === 'rename') {
        setRenameModal(true);
      } else if (key === 'remove') {
        ModalRemoveFun({
          fullName,
          name: node,
          title: 'Volume',
          requestFun: http.deleteVolume,
          successFun: removeSuccessFun,
          workspaceId: workspaceId
        });
      } else if (key === 'addFolder') {
        setShowCreateDirectoryModal(true);
      }
    },
    [node, removeSuccessFun]
  );

  // 新建 文件夹
  const [showCreateDirectoryModal, setShowCreateDirectoryModal] = useState<boolean>(false);

  // 新建文件夹弹窗关闭
  const onCancelCreateDirectory = () => {
    setShowCreateDirectoryModal(false);
  };

  // 新建文件夹成功回调
  const handleFolderCreated = (folder) => {
    changeUrlFun((pre) => ({...pre, path: pre.path ? `${pre.path}${folder}/` : `${folder}/`}));
  };

  // 新建 Volume
  const [showCreateModal, setCreateModal] = useState<boolean>(false);

  // 如果 Tab 页在概览页签，则刷新 「文件信息」列表
  const updataFilelist = debounce(() => {
    fileInfoTableRef?.current?.requestFilelist();
  }, 100);

  // 上传弹窗关闭
  const onCancelCreate = () => {
    setCreateModal(false);
  };

  const afterCloseUploadFile = () => {
    controllerRef.current.abort();
    controllerRef.current = new AbortController();
  };

  useEffect(() => {
    return () => {
      controllerRef.current.abort();
    };
  }, []);

  const renderTab = useMemo(() => {
    const config = {
      [PanelEnum.DETAIL]: <InfoPanel infoList={infoList} title="基本信息" />,
      [PanelEnum.OVERVIEW]: (
        <div>
          <DescriptionEdit
            authList={dataInfo?.privileges || []}
            text={dataInfo?.comment || ''}
            onChangeText={onChangeDescript}
            hasEdit={catalog !== CatalogType.SYSTEM}
          />
          <h2 className="title-head">文件信息</h2>
          <div>
            <VolumeFileInfoTable
              authList={dataInfo?.privileges || []}
              ref={fileInfoTableRef}
              dataInfo={dataInfo}
              urlState={urlState}
              changeUrlFun={changeUrlFun}
            />
          </div>
        </div>
      ),
      [PanelEnum.PERMISSION]: (
        <PermissionManage
          resourceType={ResourceType.Volume}
          resourceId={fullName}
          hasInheritedFrom
          name={node}
          onSuccess={getVolumeDetail}
        />
      )
    };
    return config[tab];
  }, [
    catalog,
    changeUrlFun,
    dataInfo,
    fullName,
    infoList,
    node,
    onChangeDescript,
    tab,
    urlState,
    getVolumeDetail
  ]);

  return (
    <div className="work-meta-volume-panel">
      {/* 标题导航操作栏 */}
      <MetaActionBar
        catalog={catalog}
        icon={<IconSvg type="meta-volume" size={20} color="#fff" />}
        title={node as string}
        dropdownMenu={dropdownMenu}
        authList={dataInfo?.privileges || []}
        onDropdownClick={onDropdownClick}
        createText={`上传文件到${MetaCnNameMap['Volume']}`}
        createIcon={<OutlinedButtonUpload />}
        onCreateClick={() => setCreateModal(true)}
        createAuthName={Privilege.WriteVolume}
      />
      {/* Tabs */}
      <MetaTabs
        panesList={initialPanes}
        tab={tab}
        onTabChange={onTabChange}
        authList={dataInfo?.privileges || []}
      />
      {renderTab}
      {/** 重命名 Volume 弹窗 */}
      <ModalRename
        visible={showRenameModal}
        setVisible={setRenameModal}
        fullName={fullName}
        nowValue={node}
        title="Volume"
        requestFun={http.patchVolume}
        successFun={renameSuccessFun}
        limitLength={64}
        forbidIfLimit
        nameRules={[
          {
            validator: async (_, value) => {
              // 校验特殊字符和长度限制
              if (!RULE.specialNameStartEn64.test(value)) {
                return Promise.reject(new Error(RULE.specialNameStartEn64Text));
              }
              // 异步校验Volume名称是否重复，复用查询接口 silent模式
              const res = await http.getVolumeDetail(workspaceId, `${catalog}.${schema}.${value}`, true);
              if (res.success && res.result?.id) {
                return Promise.reject(new Error('该Volume名称已存在，请重新输入'));
              }
              return Promise.resolve();
            }
          }
        ]}
      />

      {/** 上传文件到 Volume */}
      <UploadVolumeModal
        visible={showCreateModal}
        disabled
        defaultDirectory={{catalog, schema, node, path}}
        onCancel={onCancelCreate}
        onCreateSuccessCallback={updataFilelist}
        controller={controllerRef.current}
        afterCloseUploadFile={afterCloseUploadFile}
      />

      {/** 创建 Volume 文件夹弹窗 */}
      <CreateVolumeFolderModal
        visible={showCreateDirectoryModal}
        fullName={`${catalog}.${schema}.${node}`}
        onCancel={onCancelCreateDirectory}
        successCallback={handleFolderCreated}
        path={`/Volumes/${catalog}/${schema}/${node}${path ? '/' + path.slice(0, -1) : ''}`}
      />
    </div>
  );
};
export default PanelVolume;
