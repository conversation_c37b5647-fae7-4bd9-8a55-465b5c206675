import {InstanceStatus, JobStatus, FileSourceType, FileJobRunType} from '@api/integration/type';

// 操作展示形式，列表内/收起
export enum OperationShowType {
  List = 'list',
  Dropdown = 'dropdown'
}

export const JobStatusConfig = {
  [JobStatus.Ready]: {
    label: '待触发',
    icon: 'ready'
  },
  [JobStatus.Running]: {
    label: '运行中',
    icon: 'running'
  },
  [JobStatus.Success]: {
    label: '成功',
    icon: 'success'
  },
  [JobStatus.Failed]: {
    label: '失败',
    icon: 'failed'
  },
  [JobStatus.Suspend]: {
    label: '已终止',
    icon: 'suspend'
  },
  [JobStatus.Suspending]: {
    label: '终止中',
    icon: 'suspending'
  }
};

export const InstanceStatusConfig = {
  [InstanceStatus.Ready]: {
    label: '待触发',
    color: '#E6F0FF',
    textColor: '#2468F2'
  },
  [InstanceStatus.Running]: {
    label: '运行中',
    color: '#E6F0FF',
    textColor: '#2468F2'
  },
  [InstanceStatus.Failed]: {
    label: '失败',
    color: '#FFE8E6',
    textColor: '#F33E3E'
  },
  [InstanceStatus.Success]: {
    label: '成功',
    color: '#ECFFE6',
    textColor: '#30BF13'
  },
  [InstanceStatus.Stopping]: {
    label: '终止中',
    color: '#FFF4E6',
    textColor: '#FF9326'
  },
  [InstanceStatus.Stop]: {
    label: '已终止',
    color: '#F0F0F1',
    textColor: '#5C5F66'
  }
};

export const FileJobRunTypeConfig = {
  [FileJobRunType.WorkflowSchedule]: {
    label: '例行执行'
  },
  [FileJobRunType.WorkflowOnce]: {
    label: '工作流单次执行'
  },
  [FileJobRunType.Once]: {
    label: '单次执行'
  }
  // 630 版本暂无下方两个选项
  // [FileJobRunType.Rewind]: {
  //   label: '补数据执行'
  // },
  // [FileJobRunType.Rerun]: {
  //   label: '重跑'
  // }
};

export enum EditMode {
  Create = 'create',
  Edit = 'edit'
}

export enum Operation {
  Start = 'start',
  Copy = 'copy',
  Delete = 'delete',
  Edit = 'edit',
  Stop = 'stop'
}

export const DataSourceMap: Record<FileSourceType, {name: string; icon: string; color: string}> = {
  [FileSourceType.SFTP]: {
    name: 'SFTP',
    icon: 'sftp',
    color: '#F9B10B'
  },
  [FileSourceType.FTP]: {
    name: 'FTP',
    icon: 'ftp',
    color: '#00B1E3'
  }
};

export const SourceOptions = Object.keys(DataSourceMap).map((item) => ({
  label: item,
  value: DataSourceMap[item].name
}));

export enum SourceFileFilterModStatus {
  Disabled = 'disabled',
  Enabled = 'enabled'
}

export const EnableSourceFileFilterModOptions = [
  {
    label: '不过滤',
    value: SourceFileFilterModStatus.Disabled
  },
  {
    label: '过滤',
    value: SourceFileFilterModStatus.Enabled
  }
];

// 时间类型选择
export enum TimeFilterType {
  Fixed = 'fixed',
  Dynamic = 'dynamic',
  Unlimited = 'unlimited'
}

export const TimeFilterTypeOptions = [
  {
    label: '固定时间',
    value: TimeFilterType.Fixed
  },
  {
    label: '动态时间',
    value: TimeFilterType.Dynamic
  },
  {
    label: '不限',
    value: TimeFilterType.Unlimited
  }
];

// 详情页tab
export enum DetailTab {
  // 运行记录
  ProcessLog = 'process-log',
  // 任务配置
  DetailConfig = 'detail-config'
}

export const orderMap = {
  ascend: 'asc',
  descend: 'desc'
};
