/**
 * 新建实例弹窗
 * 支持自定义 工作流 全局变量
 */
import {getGlobalParams, startJob} from '@api/job';
import {WorkspaceContext} from '@pages/index';
import urls from '@utils/urls';
import {Button, Form, Input, Link, Modal, toast} from 'acud';
import {useMemoizedFn} from 'ahooks';
import React, {useContext, useEffect, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {IJobModalProps} from '.';
import JobGlobalParamsFormItem from '../FormItem/JobGlobalParamsFormItem';
import styles from './index.module.less';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import useWorkspaceAuth from '@hooks/useWorkspaceAuth';
import {Privilege} from '@api/permission/type';
import {TooltipType} from '@components/AuthComponents/constants';

const JobRunInstanceModal: React.FC<IJobModalProps> = (props: IJobModalProps) => {
  const authList = useWorkspaceAuth([Privilege.WorkflowInstanceMenu]);

  const navigate = useNavigate();
  const {jobId, jobName, onSubmit} = props;
  const [isVisible, setIsVisible] = useState(false);
  const {workspaceId} = useContext(WorkspaceContext);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 提交表单
  const handleOk = useMemoizedFn(async () => {
    setLoading(true);

    try {
      const values = await form.validateFields();
      console.log(values);
      values.globalParams = values.globalParams.filter((item: any) => item.key);

      const {result} = await startJob(workspaceId, jobId, values.globalParams);

      if (result) {
        toast.success({
          message: '运行提交成功',
          description: authList[Privilege.WorkflowInstanceMenu] ? (
            <span>
              请前往运行记录查看结果，立即前往
              <Link
                className="global-notify-ticket-link cursor-pointer"
                onClick={() => navigate(`${urls.jobResult}?jobInstanceId=${result}`)}
              >
                运行记录
              </Link>
            </span>
          ) : (
            ''
          ),
          duration: 5,
          key: result
        });
        onSubmit(true);
      }
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  });
  // 关闭弹窗
  const handleCancel = useMemoizedFn(() => {
    onSubmit(false);
  });

  const initFn = useMemoizedFn(async () => {
    const {result} = await getGlobalParams(workspaceId, jobId);
    form.setFieldsValue({
      name: jobName,
      globalParams: result || [{key: '', value: ''}]
    });
    setLoading(false);
  });

  useEffect(() => {
    initFn();
    setIsVisible(true);
  }, []);

  return (
    <Modal
      closable={true}
      width={400}
      title={'运行工作流'}
      visible={isVisible}
      onOk={handleOk}
      confirmLoading={loading}
      onCancel={handleCancel}
      className={styles['job-create-modal'] + ' ' + styles['job-create-modal-run-instance']}
    >
      <Form
        name="basic"
        layout="vertical"
        labelAlign="left"
        form={form}
        inputMaxWidth="500px"
        style={{width: '360px'}}
      >
        <Form.Item label="工作流名称" name="name">
          <Input disabled />
        </Form.Item>
        <Form.Item label="参数信息" style={{marginBottom: 0}}>
          <JobGlobalParamsFormItem />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default JobRunInstanceModal;
