import {Select} from 'acud';
import {useMemoizedFn, useRequest} from 'ahooks';
import {isArray} from 'lodash';
import React, {useEffect, useState} from 'react';
/**
 * 自定义远程下拉框，支持 Select 所有参数
 * @param queryList 查询列表 传递分页方法
 * @param id select 下拉框 对应列表字段  的 value
 * @param name select 下拉框 对应列表字段 的 label
 * @param params 查询参数
 * @param dropdownSearch 是否开启下拉框搜索
 * @param onInitData 初始化数据
 * @param onChangeSelect 原始对象
 */
interface IRemoteSelectProps extends React.ComponentProps<typeof Select> {
  queryList: (...args: any[]) => Promise<any>;
  objId?: string;
  objName?: string;
  params?: any[];
  disabledOptionFn?: (v: any) => boolean;
  onInitData?: (data: any[]) => void;
  dropdownSearch?: boolean;
  onChangeSelect?: (value: any) => void;
  dealLabel?: (value: any) => React.ReactNode;
}
const RemoteSelect: React.FC<IRemoteSelectProps> = ({
  dropdownSearch = false,
  queryList,
  objId = 'id',
  objName = 'name',
  params = [],
  disabledOptionFn = () => false,
  onInitData = () => {},
  onChangeSelect = () => {},
  dealLabel,
  ...rest
}) => {
  const [list, setList] = useState<{value: string; label: string}[]>([]);

  // 查询并且设置下拉框数据
  const {loading, run} = useRequest(() => queryList(...params), {
    manual: true,
    onSuccess: (res) => {
      const {result} = res;
      Object.keys(result).forEach((key) => {
        // 使用分页接口的数组数据
        if (isArray(result[key])) {
          const arr = [];
          // 去重 避免id 重复
          const set = new Set();
          // 给父组件传递参数
          onInitData?.(result[key]);
          result[key].forEach((item) => {
            if (!set.has(item[objId])) {
              set.add(item[objId]);
              arr.push({
                value: item[objId],
                label: dealLabel ? dealLabel(item) : item[objName],
                disabled: disabledOptionFn(item),
                sourceObj: {...item}
              });
            }
          });
          setList(arr);
        }
      });
    }
  });
  useEffect(() => {
    run();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const selectNode = useMemoizedFn((v, option) => {
    rest.onChange(v, option);
    if (isArray(option)) {
      onChangeSelect(option.map((item) => item.sourceObj));
    } else {
      onChangeSelect(option?.sourceObj);
    }
  });

  return (
    <Select
      onDropdownVisibleChange={(v) => {
        if (v && dropdownSearch) {
          run();
        }
      }}
      showSearch
      {...rest}
      loading={loading}
      options={list}
      style={{width: '100%'}}
      onChange={selectNode}
    />
  );
};

export default React.memo(RemoteSelect);
