import {Clipboard} from '@baidu/bce-react-toolkit';
import EditableContent from '@components/EditableContent';
import IconSvg from '@components/IconSvg';
import {JobTaskType} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import {setEditNodeData, setFormIsDirty} from '@store/workflow';
import {Button, Form, Input} from 'acud';
import {cloneDeep} from 'lodash';
import React, {useEffect, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {IJsonNodeData} from '../../../EditContent/X6EditPage/type';
import {nodeMap} from '../../../globalVar';
import TaskParam from './TaskParam';
import {RULE} from '@utils/regs';

const TaskBaseParams: React.FC = () => {
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  // 选中的 id
  const selectedNodeId = useSelector((state: IAppState) => state.workflowSlice.selectedNodeId);
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [detail, setDetail] = useState<IJsonNodeData | null>(null);
  const [nameArr, setNameArr] = useState<string[]>([]);
  // 初始化表单
  useEffect(() => {
    if (!selectedNodeId) {
      return;
    }
    const obj = nodeMap.get(selectedNodeId) as IJsonNodeData;
    // 设置名称数组
    const nameArr = [];
    nodeMap.forEach((value) => {
      // 有类型 的节点 并且不是当前节点
      if (value.id !== selectedNodeId && 'type' in value) {
        nameArr.push(value.name);
      }
    });
    setNameArr(nameArr);
    const objClone = cloneDeep(obj);
    setDetail(objClone);
    // 设置表单
    form.setFieldsValue(objClone);

    // 等待更新后再校验 TODO 后期需要优化 目前 0 的话 还是会有时候没有校验
    setTimeout(() => {
      form.validateFields();
    }, 100);
  }, [selectedNodeId]);

  // 修改表单 更新全局变量
  const changeForm = async (_: any, values: any) => {
    const oldObj = nodeMap.get(selectedNodeId);
    const newObj = {
      ...oldObj,
      name: values.name,
      description: values.description
    };
    nodeMap.set(selectedNodeId, newObj);
    dispatch(setEditNodeData({id: selectedNodeId, name: values.name}));

    // 设置表单脏
    dispatch(setFormIsDirty(true));
  };

  return (
    <>
      <Form
        inputMaxWidth={'100%'}
        labelAlign="left"
        layout="horizontal"
        name="basic"
        form={form}
        labelWidth={70}
        onValuesChange={changeForm}
        colon={false}
      >
        <Form.Item
          label="任务节点名称"
          name="name"
          rules={
            isEditing
              ? [
                  {required: true, message: '请输入任务节点名称'},
                  {pattern: RULE.workflowName, message: RULE.workflowNameText},
                  {
                    validator(rule, value) {
                      if (value && nameArr.includes(value)) {
                        return Promise.reject(new Error('任务节点名称已存在'));
                      }
                      return Promise.resolve();
                    }
                  }
                ]
              : []
          }
        >
          <EditableContent isEditing={isEditing}>
            <Input forbidIfLimit={true} limitLength={256} placeholder="请输入" allowClear />
          </EditableContent>
        </Form.Item>
        <Form.Item label="任务节点 ID">
          {selectedNodeId}
          <Clipboard text={selectedNodeId} className={'inline-block'}>
            <Button icon={<IconSvg type="copy" size={14} />} type="actiontext" />
          </Clipboard>
        </Form.Item>
        <Form.Item label="任务组件">{JobTaskType[detail?.type]?.label}</Form.Item>
        <Form.Item label="描述" name="description">
          <EditableContent isEditing={isEditing}>
            <Input.TextArea forbidIfLimit={true} limitLength={500} placeholder="请输入描述" allowClear />
          </EditableContent>
        </Form.Item>
      </Form>

      <TaskParam />
    </>
  );
};

export default TaskBaseParams;
