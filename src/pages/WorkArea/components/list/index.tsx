/**
 * 文件列表组件
 * <AUTHOR>
 */
import React, {useRef, useState, useEffect, useContext, useCallback, useMemo} from 'react';
import _ from 'lodash';
import {Table, Button, Search, Dropdown, Menu, toast, Modal, Loading, Tooltip} from 'acud';
import {Clipboard} from '@baidu/bce-react-toolkit';
import {Plus1} from '@baidu/xicon-react-bigdata';
import type {GetWorkspaceFileListResult, GetWorkspaceFolderListResult} from '@api/WorkArea';
import {
  deleteFile,
  deleteFolder,
  getWorkspaceFileList,
  permanentDeleteFile,
  permanentDeleteFolder,
  createNotebook
} from '@api/WorkArea';
import CopyModal, {CopyModalRef} from '../modals/CopyModal';
import RenameModal, {RenameModalRef} from '../modals/RenameModal';
import MoveModal, {MoveModalRef} from '../modals/MoveModal';
import CreateFolderModal, {CreateFolderModalRef} from '../modals/CreateFolderModal';
import ImportFileModal, {ImportFileModalRef} from '../modals/ImportFileModal';
import RestoreModal, {RestoreModalRef} from '../modals/RestoreModal';
import {useRequest} from 'ahooks';
import {WorkspaceContext} from '@pages/index';

import styles from './index.module.less';
import classNames from 'classnames/bind';
import useUrlState from '@ahooksjs/use-url-state';
import {FOLDER_NAME_MAP} from '../../config';
import IconSvg from '@components/IconSvg';
import createFolderImg from '../../../../assets/png/workarea/create-folder.png';
import importFileImg from '../../../../assets/png/workarea/import-file.png';
import createNotebookImg from '../../../../assets/png/workarea/create-notebook.png';
import PermissionModal, {PermissionModalRef} from '@components/Workspace/PermissionModal';
import {useNavigate} from 'react-router-dom';
import {PRIVILEGE_LIST} from '../../config';
import urls from '@utils/urls';
import AuthButton from '@components/AuthComponents/AuthButton';
import {Privilege} from '@api/permission/type';
import AuthMenuItem from '@components/AuthComponents/AuthMenuItem';
import useWorkspaceAuth from '@hooks/useWorkspaceAuth';
import {TooltipType} from '@components/AuthComponents/constants';

const cx = classNames.bind(styles);

interface FileListItem extends GetWorkspaceFileListResult {
  isInTrash?: boolean;
}

interface FileListProps {
  title?: string;
  currentDirObj?: GetWorkspaceFolderListResult;
  onFolderClick?: (record: GetWorkspaceFolderListResult) => void;
  onFolderChange?: (folderId: string) => void;
  onCreateFolder?: (folder: GetWorkspaceFolderListResult) => void;
}

const FileList: React.FC<FileListProps> = ({
  title,
  currentDirObj,
  onFolderClick,
  onFolderChange,
  onCreateFolder
}) => {
  const navigate = useNavigate();
  const copyModalRef = useRef<CopyModalRef>(null);
  const renameModalRef = useRef<RenameModalRef>(null);
  const moveModalRef = useRef<MoveModalRef>(null);
  const createFolderModalRef = useRef<CreateFolderModalRef>(null);
  const importFileModalRef = useRef<ImportFileModalRef>(null);
  const restoreModalRef = useRef<RestoreModalRef>(null);
  const permissionModalRef = useRef<PermissionModalRef>(null);

  const {workspaceId} = useContext(WorkspaceContext);
  const [urlState] = useUrlState();
  const [fileList, setFileList] = useState<FileListItem[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc' | undefined>(undefined);
  const [sortField, setSortField] = useState<string | undefined>(undefined);
  const [loading, setLoading] = useState(true);
  const {
    loading: runLoading,
    run: loadFileList,
    refresh
  } = useRequest(getWorkspaceFileList, {
    manual: true,
    onSuccess: (res) => {
      const list = res.result.map((item) => {
        const subMap = _.pick(FOLDER_NAME_MAP, 'TRASH', 'USERS', 'SHARED');
        const name = subMap[item.type] || item.name;
        return {
          ...item,
          name,
          isInTrash: item.status === 'DELETED',
          nodeType: name.endsWith('.ipynb') ? 'NOTEBOOK' : item.nodeType
        };
      });
      setFileList(list);
    },
    onError: (error) => {
      console.error('加载文件列表失败:', error);
    }
  });

  const isFirstLoad = useRef(true);
  useEffect(() => {
    if (isFirstLoad.current) {
      isFirstLoad.current = false;
      return;
    }
    setLoading(runLoading);
  }, [runLoading]);

  const keywordRef = useRef('');

  useEffect(() => {
    keywordRef.current = '';
    setSearchValue('');
  }, [currentDirObj]);

  useEffect(() => {
    if (currentDirObj) {
      loadFileList({
        workspaceId,
        parentId: currentDirObj.id,
        fileName: keywordRef.current,
        order: sortOrder,
        orderBy: sortField
      });
    }
  }, [currentDirObj, loadFileList, sortOrder, sortField, workspaceId]);

  function handleOpenBlank(record: GetWorkspaceFolderListResult) {
    window.open(`#/workspace/workarea?workspaceId=${workspaceId}&folderId=${record.id}`, '_blank');
  }

  // 临时删除文件、文件夹
  function handleDelete(record: GetWorkspaceFileListResult) {
    Modal.confirm({
      title: '删除',
      content: `“${record.name}” 删除后，将被移动至回收站，默认保留30天，确认是否删除？`,
      onOk: async () => {
        const api = {
          FILE: deleteFile,
          NOTEBOOK: deleteFile,
          FOLDER: deleteFolder
        }[record.nodeType || 'FILE'];
        await api({id: record.id, workspaceId});
        refreshListAndTree('DELETE')(record);
      }
    });
  }

  // 永久删除文件、文件夹
  function handlePermanentDelete(record: GetWorkspaceFileListResult) {
    Modal.confirm({
      title: '永久删除',
      content: `“${record.name}” 将被永久删除，此操作无法撤销，所有相关数据将被清空，请谨慎操作。`,
      onOk: async () => {
        const api = {
          FILE: permanentDeleteFile,
          NOTEBOOK: permanentDeleteFile,
          FOLDER: permanentDeleteFolder
        }[record.nodeType || 'FILE'];
        await api({id: record.id, workspaceId});
        refresh();
      }
    });
  }

  // 清空回收站
  function handleClearTrash(trashId: string) {
    if (!trashId) {
      return;
    }
    Modal.confirm({
      title: '清空回收站',
      content:
        '你确定要永久删除回收站中的所有项目吗？此操作无法撤销。注意：回收站中的项目会在 30 天后自动被永久删除。',
      onOk: async () => {
        await permanentDeleteFolder({id: trashId, workspaceId});
        refresh();
      }
    });
  }

  function onNotebookClick(record: GetWorkspaceFileListResult) {
    navigate(
      `${urls.notebook}?workspaceId=${workspaceId}&folderId=${record.parentId}&notebookId=${record.id}`
    );
  }

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: {
        showTitle: false
      },
      render: (text: string, record: FileListItem) => {
        const iconSize = {size: 16};

        const iconMap = {
          FOLDER: (
            <IconSvg className={cx('file-icon')} type="workarea-folder" color="#FF9F0B" {...iconSize} />
          ),
          FILE: <IconSvg className={cx('file-icon')} type="workarea-file" color="#151B26" {...iconSize} />,
          NOTEBOOK: (
            <IconSvg
              className={cx('file-icon')}
              type="nb-notebook"
              color="#2468F2"
              fill="none"
              {...iconSize}
            />
          )
        };
        let icon = iconMap[record.nodeType || 'FILE'];

        if (record.type === 'TRASH') {
          icon = <IconSvg className={cx('file-icon')} type="workarea-trash" color="#84868C" {...iconSize} />;
        }

        if (record.type === 'SHARED') {
          icon = (
            <IconSvg
              className={cx('file-icon')}
              type="workarea-shared"
              color="#84868C"
              {...iconSize}
              fill="transparent"
            />
          );
        }

        if (record.nodeType === 'FOLDER') {
          return (
            <>
              {icon}
              <Tooltip placement="topLeft" title={text}>
                {record.isInTrash ? text : <a onClick={() => onFolderClick?.(record)}>{text}</a>}
              </Tooltip>
            </>
          );
        }
        if (record.nodeType === 'NOTEBOOK') {
          return (
            <>
              {icon}
              <Tooltip placement="topLeft" title={text}>
                {record.isInTrash ? text : <a onClick={() => onNotebookClick(record)}>{text}</a>}
              </Tooltip>
            </>
          );
        }
        return (
          <>
            {icon}
            <Tooltip placement="topLeft" title={text}>
              {text}
            </Tooltip>
          </>
        );
      }
    },
    {
      title: '类型',
      dataIndex: 'nodeType',
      key: 'nodeType',
      width: 100,
      render: (text: string) => {
        const typeMap = {
          FOLDER: '文件夹',
          FILE: '文件',
          NOTEBOOK: 'Notebook'
        };
        return typeMap[text];
      }
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      key: 'creator',
      width: 120
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      sorter: true
    },
    {
      title: '操作',
      key: 'operation',
      width: 100,
      render: (_, record: FileListItem) => {
        const copyPath = (
          <Menu.Item key="copyPath">
            <Clipboard text={record.path} successMessage="复制路径成功">
              <span>复制路径</span>
            </Clipboard>
          </Menu.Item>
        );

        const copy = (
          <Menu.Item
            key="copy"
            onClick={() => {
              copyModalRef.current?.open(record);
            }}
          >
            复制
          </Menu.Item>
        );

        const rename = (
          <Menu.Item
            key="rename"
            onClick={() => {
              renameModalRef.current?.open(record);
            }}
          >
            重命名
          </Menu.Item>
        );

        const move = (
          <Menu.Item
            key="move"
            onClick={() => {
              moveModalRef.current?.open(record);
            }}
          >
            移动
          </Menu.Item>
        );

        const deleteItem = (
          <Menu.Item
            key="delete"
            onClick={() => {
              handleDelete(record);
            }}
          >
            删除
          </Menu.Item>
        );

        const openBlank = (
          <Menu.Item key="openBlank" onClick={() => handleOpenBlank(record)}>
            在新窗口打开
          </Menu.Item>
        );

        const newFolder = (
          <Menu.Item
            key="newFolder"
            onClick={() => {
              createFolderModalRef.current?.open(record);
            }}
          >
            新建文件夹
          </Menu.Item>
        );

        const importFile = (
          <Menu.Item
            key="importFile"
            onClick={() => {
              importFileModalRef.current?.open(record);
            }}
          >
            导入文件
          </Menu.Item>
        );

        const restore = (
          <Menu.Item
            key="restore"
            onClick={() => {
              restoreModalRef.current?.open(record);
            }}
          >
            恢复
          </Menu.Item>
        );

        const permanentDelete = (
          <Menu.Item
            key="permanentDelete"
            onClick={() => {
              handlePermanentDelete(record);
            }}
          >
            永久删除
          </Menu.Item>
        );

        const permission = (
          <Menu.Item
            key="permission"
            onClick={() => {
              const resourceTypeMap = {
                FOLDER: 'DIRECTORY',
                FILE: 'FILE',
                NOTEBOOK: 'NOTEBOOK'
              };
              permissionModalRef.current?.open({
                resourceType: resourceTypeMap[record.nodeType],
                resourceId: record.id,
                resourceName: record.name,
                privilegeList: PRIVILEGE_LIST
              });
            }}
          >
            权限管理
          </Menu.Item>
        );

        let menuItems: React.ReactNode[] = [];
        // 普通文件夹
        if (record.nodeType === 'FOLDER') {
          const menuItemsMap = {
            HOME: [openBlank, newFolder, importFile, copyPath],
            USER: [openBlank, newFolder, importFile, copyPath],
            USERS: [openBlank, copyPath],
            TRASH: [openBlank],
            NORMAL: [openBlank, newFolder, importFile, copy, copyPath, rename, move, deleteItem],
            SHARED: [openBlank, newFolder, importFile, copyPath]
          };
          menuItems = menuItemsMap[record.type] || menuItemsMap.NORMAL;
        }
        // 普通文件
        if (record.nodeType === 'FILE' || record.nodeType === 'NOTEBOOK') {
          menuItems = [copy, copyPath, rename, move, deleteItem];
        }
        // 回收站内容
        if (record.isInTrash) {
          menuItems = [restore, permanentDelete];
        }

        const menu = <Menu>{menuItems}</Menu>;

        return (
          <Dropdown overlay={menu} trigger={['hover']}>
            <a>
              <IconSvg className={cx('file-icon')} type="more" color="#151B26" size={16} />
            </a>
          </Dropdown>
        );
      }
    }
  ];

  const onMenuClick = useCallback(
    ({key}) => {
      if (key === 'import') {
        importFileModalRef.current?.open(currentDirObj!);
      }
      // copyPath 现在由 Clipboard 组件处理，不需要在这里处理
    },
    [currentDirObj]
  );

  const handleTableChange = useCallback((pagination, filters, sorter) => {
    setSortField(sorter.field);

    const orderMap = {
      ascend: 'asc',
      descend: 'desc'
    };
    setSortOrder(orderMap[sorter.order]);
  }, []);

  function refreshListAndTree(action: string) {
    return (record: GetWorkspaceFileListResult, targetDir?: string) => {
      refresh();
      onFolderChange?.(urlState.folderId);
      if (action === 'MOVE' || action === 'RESTORE') {
        onFolderChange?.(targetDir);
      }
    };
  }

  const {run: createNotebookRun} = useRequest(createNotebook, {
    manual: true,
    onSuccess: (res) => {
      if (res.success && res.result) {
        navigate(
          `${urls.notebook}?workspaceId=${workspaceId}&folderId=${urlState.folderId}&notebookId=${res.result.id}`
        );
      }
    }
  });

  function handleCreateNotebook(record: GetWorkspaceFolderListResult) {
    createNotebookRun({
      workspaceId,
      parentId: record.id
    });
  }

  const blankBlock = (
    <div className={cx('blank-space', 'flex-1')}>
      <div className={cx('card-wrap')}>
        <div className={cx('create-card')}>
          <img className={cx('img', 'w-full')} src={createFolderImg} />
          <div className={cx('title')}>创建文件夹</div>
          <div className={cx('description')}>适用于文件分类</div>
          <div className={cx('btn')}>
            <Button type="primary" onClick={() => createFolderModalRef.current?.open(currentDirObj)}>
              立即创建
            </Button>
          </div>
        </div>
        <div className={cx('create-card')}>
          <img className={cx('img', 'w-full')} src={createNotebookImg} />
          <div className={cx('title')}>创建Notebook</div>
          <div className={cx('description')}>适用于自定义任务的交互式开发</div>
          <div className={cx('btn')}>
            <Button type="primary" onClick={() => handleCreateNotebook(currentDirObj)}>
              立即创建
            </Button>
          </div>
        </div>
        <div className={cx('create-card')}>
          <img className={cx('img', 'w-full')} src={importFileImg} />
          <div className={cx('title')}>导入文件</div>
          <div className={cx('description')}>适用于离线开发的工程文件</div>
          <div className={cx('btn')}>
            <Button type="primary" onClick={() => importFileModalRef.current?.open(currentDirObj)}>
              立即导入
            </Button>
          </div>
        </div>
      </div>
    </div>
  );

  const createDisabled = ['ALL', 'USERS', 'TRASH'].includes(currentDirObj?.type || '');

  const showBlank = fileList.length === 0 && keywordRef.current === '' && currentDirObj?.type === 'HOME';

  return (
    <div className={cx('h-full', 'file-list', 'relative')}>
      <h1 className={cx('list-title', 'break-all', 'mb-[16px]')}>{title}</h1>
      {loading ? (
        <Loading loading={true} />
      ) : showBlank ? (
        blankBlock
      ) : (
        <>
          <div className="flex justify-between mb-[16px]">
            <Search
              value={searchValue}
              placeholder="请输入名称搜索"
              className="w-[240px]"
              onSearch={(value) => {
                keywordRef.current = value;
                loadFileList({
                  workspaceId,
                  parentId: currentDirObj.id,
                  fileName: keywordRef.current,
                  order: sortOrder,
                  orderBy: sortField
                });
              }}
              onChange={(e) => {
                setSearchValue(e.target.value);
              }}
              allowClear
            />
            <div className="flex items-center">
              {currentDirObj?.type !== 'TRASH' ? (
                <>
                  <Dropdown
                    overlay={
                      <Menu onClick={onMenuClick}>
                        <Menu.Item key="import" disabled={createDisabled}>
                          导入文件
                        </Menu.Item>
                        <Menu.Item key="copyPath">
                          <Clipboard text={currentDirObj?.path || ''} successMessage="复制路径成功">
                            <span>复制路径</span>
                          </Clipboard>
                        </Menu.Item>
                      </Menu>
                    }
                  >
                    <Button className="db-more-button" icon={<IconSvg type="more" size={16} />}></Button>
                  </Dropdown>
                  <Dropdown
                    overlay={
                      <Menu>
                        <Menu.Item
                          key="newFolder"
                          disabled={createDisabled}
                          onClick={() => handleCreateNotebook(currentDirObj!)}
                        >
                          Notebook
                        </Menu.Item>
                        <Menu.Item
                          key="newFolder"
                          disabled={createDisabled}
                          onClick={() => createFolderModalRef.current?.open(currentDirObj!)}
                        >
                          文件夹
                        </Menu.Item>
                      </Menu>
                    }
                    placement="bottomRight"
                  >
                    <Button
                      type="primary"
                      className="ml-[8px]"
                      disabled={createDisabled}
                      icon={<Plus1 size={15} />}
                    >
                      创建
                    </Button>
                  </Dropdown>
                </>
              ) : (
                <Button className="ml-[8px]" onClick={() => handleClearTrash(currentDirObj?.id || '')}>
                  永久删除
                </Button>
              )}
            </div>
          </div>
          <Table
            loading={loading}
            dataSource={fileList}
            columns={columns}
            rowKey="id"
            pagination={false}
            onChange={handleTableChange}
          />
        </>
      )}

      <CopyModal workspaceId={workspaceId} ref={copyModalRef} onSuccess={refreshListAndTree('COPY')} />
      <RenameModal workspaceId={workspaceId} ref={renameModalRef} onSuccess={refreshListAndTree('RENAME')} />
      <MoveModal workspaceId={workspaceId} ref={moveModalRef} onSuccess={refreshListAndTree('MOVE')} />
      <CreateFolderModal workspaceId={workspaceId} ref={createFolderModalRef} onSuccess={onCreateFolder} />
      <ImportFileModal workspaceId={workspaceId} ref={importFileModalRef} onSuccess={refresh} />
      <RestoreModal
        workspaceId={workspaceId}
        ref={restoreModalRef}
        onSuccess={refreshListAndTree('RESTORE')}
      />
      <PermissionModal workspaceId={workspaceId} ref={permissionModalRef} />
    </div>
  );
};

export default FileList;
