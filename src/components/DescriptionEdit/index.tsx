/**
 * DescriptionEdit 组件
 *
 * 该组件用于显示和编辑描述文本。支持通过点击编辑按钮进入编辑模式，
 * 编辑完成后可以通过确认按钮保存修改，或通过取消按钮放弃修改。
 * <AUTHOR>
 */

import React, {useEffect, useRef, useState} from 'react';
import {Button, Input} from 'acud';
import {useBoolean} from 'ahooks';
import {Edit2} from '@baidu/xicon-react-bigdata';
import cx from 'classnames';
import AuthButton from '@components/AuthComponents/AuthButton';
import {Privilege} from '@api/permission/type';
import './index.less';
interface IDescriptionEditProps {
  text: string; // 描述文本
  hasEdit?: boolean; // 是否支持编辑
  onChangeText?: (value: string) => void; // 编辑完成确认回调函数
  limitLength?: number;
  className?: string;
  authList?: string[]; // 权限列表
}

const klass = 'description-edit-com';

const DescriptionEdit = (props: IDescriptionEditProps) => {
  const {text, hasEdit = true, onChangeText, limitLength = 150, className, authList = []} = props;
  const [isEdit, {setTrue, setFalse}] = useBoolean(false);
  const textValueRef = useRef(text);
  const [curValue, setCurValue] = useState(text);

  // 更新描述
  const onClickeOk = () => {
    setFalse();
    setCurValue(text);
    // 传递上层具体处理
    onChangeText && onChangeText(textValueRef.current);
  };

  const onInput = (e) => {
    setCurValue(e.target.value);
    textValueRef.current = e.target.value;
  };

  useEffect(() => {
    setCurValue(text);
  }, [text]);

  return (
    <div className={cx(klass, className)}>
      <h4 className={`${klass}-title`}>
        描述
        {hasEdit ? (
          <AuthButton
            isAuth={authList.length && authList.includes(Privilege.Manage)}
            type="text"
            onClick={setTrue}
          >
            <Edit2 theme="line" size={16} strokeLinejoin="round" />
          </AuthButton>
        ) : null}
      </h4>
      {isEdit ? (
        <>
          <Input.TextArea
            value={curValue}
            placeholder="请添加描述"
            allowClear={false}
            autoSize={{minRows: 2, maxRows: 4}}
            onChange={onInput}
            limitLength={limitLength}
            forbidIfLimit={true}
          />
          <div className={`${klass}-group`}>
            <Button type="primary" size="small" onClick={onClickeOk}>
              确定
            </Button>
            <Button size="small" onClick={setFalse}>
              取消
            </Button>
          </div>
        </>
      ) : text.length === 0 ? (
        <p className={`${klass}-placeholder`}>请添加描述</p>
      ) : (
        <p className={`${klass}-text`}>{text}</p>
      )}
    </div>
  );
};

export default React.memo(DescriptionEdit);
