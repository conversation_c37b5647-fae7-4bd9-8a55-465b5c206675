import {startIntegrationJob} from '@api/integration';
import {IJobItem, JobTriggerType} from '@api/integration/type';
import urls from '@utils/urls';
import {Al<PERSON>, Button, Collapse, DatePicker, Link, Modal, Space, toast} from 'acud';
import {useMemoizedFn} from 'ahooks';
import moment, {Moment} from 'moment';
import React, {useState} from 'react';
const {Panel} = Collapse;

// 运行任务弹窗
const StartJobModal: React.FC<{
  jobs: IJobItem[];
  workspaceId: string;
  callback: () => void;
  navigate: (path: string) => void;
  afterClose: () => void;
}> = ({jobs, workspaceId, callback, navigate, afterClose}) => {
  const [visible, setVisible] = useState(true);
  const [scheduleTime, setScheduleTime] = useState<Moment>(moment());

  // 运行任务 是否查看详情
  const runJob = useMemoizedFn(async (flag: boolean) => {
    const {result, success} = await startIntegrationJob(workspaceId, jobs[0].jobId, {
      isPublished: true,
      runtimeArgs: {
        triggerType: JobTriggerType.Once,
        scheduleTime: scheduleTime.format('YYYY-MM-DD HH:mm:ss')
      }
    });

    if (!success) {
      return;
    }
    setVisible(false);

    const {runId, jobId} = result;

    if (flag) {
      navigate(`${urls.offlineCollectResult}?jobId=${jobId}&runId=${runId}&taskName=${jobs[0]?.name}`);

      toast.success({
        message: '运行成功！',
        duration: 5
      });
    } else {
      toast.success({
        message: '运行提交成功',
        description: (
          <span>
            请前往运行记录查看结果，立即前往
            <Link
              className="global-notify-ticket-link cursor-pointer"
              onClick={() =>
                navigate(
                  `${urls.offlineCollectResult}?jobId=${jobId}&runId=${runId}&taskName=${jobs[0]?.name}`
                )
              }
            >
              运行记录
            </Link>
          </span>
        ),
        duration: 5
      });
      callback();
    }
  });

  const footer = (
    <Space>
      <Button type="default" onClick={() => setVisible(false)}>
        取消
      </Button>
      <Button type="enhance" onClick={() => runJob(false)}>
        运行
      </Button>

      <Button type="primary" onClick={() => runJob(true)}>
        运行并查看详情
      </Button>
    </Space>
  );
  return (
    <Modal
      visible={visible}
      title={`运行`}
      width={632}
      footer={footer}
      onCancel={() => setVisible(false)}
      afterClose={afterClose}
    >
      <Alert
        message="您可在「任务列表-最近运行」查看正在执行的任务，通过任务名称查看该任务的详细运行记录"
        type="info"
      />
      <div className="flex items-center pt-4">
        <span className="mr-2">业务时间</span>
        <DatePicker value={scheduleTime} onChange={setScheduleTime} showTime format="YYYY-MM-DD HH:mm:ss" />
      </div>
    </Modal>
  );
};

export default StartJobModal;
