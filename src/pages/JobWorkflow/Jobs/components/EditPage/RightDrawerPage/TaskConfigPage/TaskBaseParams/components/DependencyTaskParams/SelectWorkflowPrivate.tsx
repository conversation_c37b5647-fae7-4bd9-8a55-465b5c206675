import {detailJob} from '@api/job';
import {
  IWorkspaceObj,
  jobDetailPrivate,
  jobListPrivate,
  workspacesDetailPrivate,
  workspacesListPrivate,
  WorkspaceType
} from '@api/workflow/private';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import EditableContent from '@components/EditableContent';
import RemoteSelect from '@components/RemoteSelect';
import {JobDependencyTypeChineseMap, JobDependencyTypeEnum} from '@pages/JobWorkflow/constants';
import {IJsonNodeData} from '@pages/JobWorkflow/Jobs/components/EditPage/EditContent/X6EditPage/type';
import {nodeMap} from '@pages/JobWorkflow/Jobs/components/EditPage/globalVar';
import {IAppState} from '@store/index';
import {Form, Select, Tag} from 'acud';
import {FormInstance} from 'acud/lib/form';
import {OptGroup} from 'acud/lib/select/src';
import {useDebounceFn, useMemoizedFn} from 'ahooks';
import React, {useEffect, useMemo, useState} from 'react';
import {useSelector} from 'react-redux';

const SelectWorkflowPrivate: React.FC<{form: FormInstance}> = ({form}) => {
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const selectedNodeId = useSelector((state: IAppState) => state.workflowSlice.selectedNodeId);

  const workspaceId = Form.useWatch('workspaceId', form);
  const jobId = Form.useWatch('jobId', form);
  const depTaskId = Form.useWatch('depTaskId', form);

  const [dependencyType, setDependencyType] = useState<JobDependencyTypeEnum>(
    JobDependencyTypeEnum.DEPENDENT_JOB
  );

  const [taskNode, setTaskNode] = useState<React.ReactNode>('-');

  const [jobCode, setJobCode] = useState<string>('');

  // 工作空间列表
  const [workspaceList, setWorkspaceList] = useState<IWorkspaceObj[]>([]);

  const workspaceType = Form.useWatch('workspaceType', form);

  // 依赖任务选项
  const depTaskIdOptions = useMemo(() => {
    if (!jobCode) {
      return [];
    }
    const arr = [];
    try {
      const jsonObj = JSON.parse(jobCode || '{}');
      jsonObj?.taskDefinitionList?.forEach((item) => {
        arr.push({label: item?.name, value: item?.id});
      });
    } catch (error) {
      console.error(error);
    }
    return arr;
  }, [jobCode]);

  // 处理工作流名称
  const dealJobId = useMemoizedFn(async () => {
    if (!workspaceId || !jobId) {
      return;
    }

    // EDAP 只需要处理 节点名称
    if (workspaceType === WorkspaceType.STRUCTURED) {
      return;
    }
    const {result} = await detailJob(workspaceId, jobId);

    let taskNodeName = '-';
    try {
      const jsonObj = JSON.parse(result?.code || '{}');
      setJobCode(result?.code);

      for (const item of jsonObj?.taskDefinitionList || []) {
        if (depTaskId === item.id) {
          taskNodeName = item.name;
        }
      }
    } catch (error) {
      console.error(error);
    }
    setTaskNode(<Ellipsis tooltip={taskNodeName}>{taskNodeName}</Ellipsis>);
  });

  // 处理任务下的节点列表
  useEffect(() => {
    dealJobId();
  }, [workspaceType, jobId, selectedNodeId]);

  // 初始化 是依赖任务 还是依赖工作流
  useEffect(() => {
    const obj = nodeMap.get(selectedNodeId) as IJsonNodeData;
    const taskParam: any = obj?.taskParam;

    setDependencyType(
      taskParam?.depTaskId ? JobDependencyTypeEnum.DEPENDENT_TASK : JobDependencyTypeEnum.DEPENDENT_JOB
    );
  }, [isEditing, selectedNodeId]);

  // 修改任务参数表单
  const changeTaskParam = useMemoizedFn(async (key: string, value: any) => {
    form.setFieldValue(key, value);
    const oldObj = nodeMap.get(selectedNodeId) as IJsonNodeData;

    const newObj = {
      ...oldObj,
      taskParam: {
        ...oldObj.taskParam,
        [key]: value
      }
    };
    nodeMap.set(selectedNodeId, newObj);
  });

  // 处理工作流名称改变
  const handleJobChange = useMemoizedFn((v: any) => {
    changeTaskParam('depTaskId', null);
  });

  //
  const onChangeSelectWorkspace = useMemoizedFn((value: IWorkspaceObj) => {
    const workspaceTypeValue = value ? value.workspaceType : undefined;
    changeTaskParam('workspaceType', workspaceTypeValue);
    changeTaskParam('depTaskId', null);
    changeTaskParam('jobId', null);
    setDependencyType(JobDependencyTypeEnum.DEPENDENT_JOB);
  });

  // 初始化工作空间
  const initWorkspace = useMemoizedFn(() => {
    workspacesListPrivate().then((res) => {
      setWorkspaceList(res.result.items);
    });
  });
  useEffect(() => {
    initWorkspace();
  }, []);

  const workspaceTypeTypeChineseMap = {
    [WorkspaceType.MULTIMODAL]: '多模态',
    [WorkspaceType.STRUCTURED]: '结构化'
  };

  return (
    <>
      <Form.Item name="workspaceType" noStyle hidden></Form.Item>
      <Form.Item
        label="工作空间"
        name="workspaceId"
        rules={isEditing ? [{required: true, message: '请选择工作空间'}] : []}
      >
        <EditableContent
          isEditing={isEditing}
          getDetail={(value) =>
            workspacesDetailPrivate(value, workspaceType).then((res) => {
              return (
                <>
                  {res.result?.name} <Tag>{workspaceTypeTypeChineseMap[workspaceType]}</Tag>
                </>
              );
            })
          }
        >
          <Select
            showSearch
            className="w-full"
            keepExpand
            groupSelectorRender={(groupLabel, value) => <>{value}</>}
            optionFilterProp="label"
            dropdownStyle={{maxWidth: 300}}
            onSelect={(value) => {
              const workspace = workspaceList.find((item) => item.id === value);
              onChangeSelectWorkspace(workspace);
            }}
            dropdownMatchSelectWidth={false}
          >
            <OptGroup label={workspaceTypeTypeChineseMap[WorkspaceType.MULTIMODAL]} key="manager">
              {workspaceList
                .filter((item) => item.workspaceType === WorkspaceType.MULTIMODAL)
                .map((item) => (
                  <Select.Option key={item.id} value={item.id}>
                    {item.name}
                  </Select.Option>
                ))}
            </OptGroup>
            <OptGroup label={workspaceTypeTypeChineseMap[WorkspaceType.STRUCTURED]} key="manager1">
              {workspaceList
                .filter((item) => item.workspaceType === WorkspaceType.STRUCTURED)
                .map((item) => (
                  <Select.Option key={item.id} value={item.id}>
                    {item.name}
                  </Select.Option>
                ))}
            </OptGroup>
          </Select>
        </EditableContent>
      </Form.Item>

      <Form.Item label="依赖类型">
        {isEditing ? (
          <Select
            className="w-full"
            disabled={workspaceType === WorkspaceType.STRUCTURED}
            defaultValue={dependencyType}
            value={dependencyType}
            onSelect={(v) => {
              setDependencyType(v as JobDependencyTypeEnum);
              changeTaskParam('depTaskId', null);
            }}
            options={Object.entries(JobDependencyTypeChineseMap).map(([key, value]) => ({
              label: value,
              value: key
            }))}
          />
        ) : (
          JobDependencyTypeChineseMap[dependencyType]
        )}
      </Form.Item>
      <Form.Item
        shouldUpdate={(prevValues, curValues) => prevValues.workspaceId !== curValues.workspaceId}
        noStyle
      >
        {({getFieldValue}) => {
          const workspaceId = getFieldValue('workspaceId');
          const workspaceType = getFieldValue('workspaceType');
          const params = [workspaceId, workspaceType];

          console.log(workspaceId);
          if (!workspaceId) {
            return <></>;
          }

          return (
            <Form.Item
              label="工作流名称"
              name="jobId"
              rules={isEditing ? [{required: true, message: '请选择工作流'}] : []}
            >
              <EditableContent
                isEditing={isEditing}
                getDetail={(value) =>
                  jobDetailPrivate(workspaceId, workspaceType, value).then((res) => {
                    return res.result.name;
                  })
                }
              >
                <RemoteSelect
                  showSearch
                  dropdownSearch={true}
                  optionFilterProp="label"
                  dropdownMatchSelectWidth={false}
                  showTitle={true}
                  dropdownStyle={{maxWidth: 300}}
                  disabled={!workspaceId}
                  objName="name"
                  objId={'jobId'}
                  queryList={jobListPrivate}
                  params={params}
                  placeholder="全部工作流"
                  onSelect={handleJobChange}
                />
              </EditableContent>
            </Form.Item>
          );
        }}
      </Form.Item>
      {dependencyType === JobDependencyTypeEnum.DEPENDENT_TASK && (
        <Form.Item shouldUpdate={(prevValues, curValues) => prevValues.jobId !== curValues.jobId} noStyle>
          {() => {
            return (
              <>
                <Form.Item label="任务名称" name="depTaskId">
                  <EditableContent isEditing={isEditing} dealValue={() => taskNode}>
                    <Select
                      showSearch
                      optionFilterProp="label"
                      options={depTaskIdOptions}
                      className="w-full"
                    />
                  </EditableContent>
                </Form.Item>
              </>
            );
          }}
        </Form.Item>
      )}
    </>
  );
};

export default SelectWorkflowPrivate;
