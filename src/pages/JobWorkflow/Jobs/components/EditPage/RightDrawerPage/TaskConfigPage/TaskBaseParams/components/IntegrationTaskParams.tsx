import {getIntegrationJobList, getJobDetails} from '@api/integration';
import {JobType} from '@api/integration/type';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import EditableContent from '@components/EditableContent';
import IconSvg from '@components/IconSvg';
import RemoteSelect from '@components/RemoteSelect';
import {WorkspaceContext} from '@pages/index';
import {JobNodeTypeEnum} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import urls from '@utils/urls';
import {Form} from 'acud';
import {FormInstance} from 'acud/lib/form';
import {useMemoizedFn} from 'ahooks';
import React, {useContext, useEffect, useMemo, useState} from 'react';
import {useSelector} from 'react-redux';
import {IJsonNodeData} from '../../../../EditContent/X6EditPage/type';
import {nodeMap} from '../../../../globalVar';
import {Privilege} from '@api/permission/type';
import {authCheck} from '@pages/JobWorkflow/tools';

const IntegrationTaskParams: React.FC<{form?: FormInstance; type: JobNodeTypeEnum}> = ({form, type}) => {
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const selectedNodeId = useSelector((state: IAppState) => state.workflowSlice.selectedNodeId);
  const {workspaceId} = useContext(WorkspaceContext);
  const [jobName, setJobName] = useState<React.ReactNode>('');

  const jobType = useMemo(() => {
    return type === JobNodeTypeEnum.FILE_INTEGRATION_TASK ? JobType.File : JobType.Batch;
  }, [type]);

  const nameIcon = useMemoizedFn((label: string, id: string) => {
    return (
      <div
        style={{
          width: '100%',
          display: 'flex',
          gap: 8,
          alignItems: 'center'
        }}
      >
        <Ellipsis tooltip={label}>{label}</Ellipsis>
        <IconSvg
          className="cursor-pointer"
          color="#2468f2"
          type="open"
          size={16}
          onClick={(e) => {
            if (type === JobNodeTypeEnum.FILE_INTEGRATION_TASK) {
              window.open(`#${urls.fileCollectDetail}?jobId=${id}&workspaceId=${workspaceId}`, '_blank');
            } else if (type === JobNodeTypeEnum.TABLE_INTEGRATION_TASK) {
              window.open(`#${urls.offlineCollectDetail}?jobId=${id}&workspaceId=${workspaceId}`, '_blank');
            }
            e.stopPropagation();
          }}
        />
      </div>
    );
  });

  // 根据 jobId 获取任务名称
  const dealJobIdToName = useMemoizedFn(async (id: string) => {
    if (!workspaceId || !id) {
      return;
    }
    const {result} = await getJobDetails(workspaceId, id);
    setJobName(nameIcon(result?.name, id));
  });
  useEffect(() => {
    const obj = nodeMap.get(selectedNodeId) as IJsonNodeData;
    const taskParam: any = obj?.taskParam;
    if (!isEditing) {
      dealJobIdToName(taskParam?.integrationJobId);
    }
  }, [isEditing, selectedNodeId]);
  return (
    <>
      <Form.Item
        label="任务名称"
        name="integrationJobId"
        rules={isEditing ? [{required: true, message: '请选择任务'}] : []}
      >
        <EditableContent isEditing={isEditing} dealValue={() => jobName}>
          <RemoteSelect
            dropdownMatchSelectWidth={false}
            showTitle={true}
            dropdownStyle={{maxWidth: 300}}
            optionFilterProp="label"
            objName="name"
            objId="jobId"
            queryList={getIntegrationJobList}
            params={[workspaceId, {type: jobType, pageNo: 1, pageSize: 10000}]}
            placeholder="请选择任务"
            optionRender={(label, option) => {
              return nameIcon(option.label, String(option.value));
            }}
            disabledOptionFn={(v) => {
              return !authCheck(v?.privileges, Privilege.Execute);
            }}
          />
        </EditableContent>
      </Form.Item>
    </>
  );
};

export default IntegrationTaskParams;
