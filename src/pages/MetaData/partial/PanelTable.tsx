/**
 * Table 面板
 * 注意：
 * 1、catalog 为 CatalogType.SYSTEM 和 CatalogType.EDAP_DATALAKE 类型下的 Table 不支持编辑/删除
 * 2、catalog 为 CatalogType.EDAP_DATALAKE 类型下只有 Table
 * <AUTHOR>
 */
import {useCallback, useContext, useEffect, useMemo, useState} from 'react';
import {Button, Search, Table} from 'acud';
import {useAsyncEffect, useRequest} from 'ahooks';

import DescriptionEdit from '@components/DescriptionEdit';
import InfoPanel from '@components/InfoPanel';
import IconSvg from '@components/IconSvg';

import * as http from '@api/metaRequest';
import {IUrlStateHandler} from '../index';

import MetaActionBar from '../components/MetaActionBar';
import MetaTabs from '../components/MetaTabs';
import ModalRemoveFun from '../components/ModalRemoveFun';
import ModalRename from '../components/ModalRename';
import {RULE} from '@utils/regs';
import {MetaCnNameMap, ruleMapByCatalog} from '../config';
import {CatalogType} from '@api/metaRequest';
import PermissionManage from '../../../components/PermissionManage';
import {Privilege, ResourceType} from '@api/permission/type';
import {isBuildInCatalog} from '../helper';
import {edapIframeSrc} from '@components/IframePreloader';
import {WorkspaceContext} from '@pages/index';

enum PanelEnum {
  OVERVIEW = '1',
  DETAIL = '2',
  PERMISSION = '3'
}

const columns = [
  {
    title: '字段名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '字段类型',
    dataIndex: 'typeName',
    key: 'typeName'
  },
  {
    title: '字段描述',
    dataIndex: 'comment',
    key: 'comment',
    render: (text) => text || '-'
  }
];

const PanelTable = (props: IUrlStateHandler) => {
  const {urlState, changeUrlFun, changeUrlFunReplace, userList} = props;
  const {catalog = '', schema = '', node = '', tab = PanelEnum.OVERVIEW} = urlState;

  // catalog 类型
  const [catalogType, setCatalogType] = useState<CatalogType>(CatalogType.SYSTEM);

  const dropdownMenu = useMemo(
    () => [
      {
        key: 'rename',
        label: `重命名${MetaCnNameMap['Table']}`,
        authName: Privilege.Manage
      },
      {key: 'remove', label: `删除${MetaCnNameMap['Table']}`, authName: Privilege.Manage}
    ],
    []
  );

  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);
  // table 全名
  const fullName = `${catalog}.${schema}.${node}`;

  // 详情 & 概览
  const [dataInfo, setDataInfo] = useState<http.ITableDetailRes>();

  const initialPanes = useMemo(
    () => [
      {tab: '概览', key: PanelEnum.OVERVIEW},
      {tab: '详情', key: PanelEnum.DETAIL},
      ...(isBuildInCatalog(catalog as CatalogType)
        ? []
        : [{tab: '权限管理', key: PanelEnum.PERMISSION, privilege: [Privilege.Manage]}])
    ],
    [catalog]
  );

  // 详情字段类型配置
  const tableInfoFieldsMap = useMemo(
    () => ({
      [CatalogType.EDAP_DATALAKE]: [
        'catalogName',
        'schemaName',
        'tableName',
        'tableType',
        'dataSourceFormat',
        'storageLocation',
        'createdAt',
        'createdBy'
      ],
      [CatalogType.DORIS]: [
        'catalogName',
        'schemaName',
        'tableName',
        'tableType',
        'dataSourceFormat',
        'createdAt'
      ],
      [CatalogType.DATALAKE]: [
        'catalogName',
        'schemaName',
        'tableName',
        'tableType',
        'dataSourceFormat',
        'storageLocation',
        'createdAt',
        'createdBy',
        'updatedAt',
        'updatedBy'
      ]
      // 其他类型可按需补充
    }),
    []
  );

  // 详情字段
  const infoList = useMemo(() => {
    const info: any = dataInfo || {};
    // 获取要展示的字段 list，如找不到默认使用 DATALAKE 类型
    const fields = tableInfoFieldsMap[catalogType] || tableInfoFieldsMap[CatalogType.DATALAKE];
    return fields
      .map((key) => {
        switch (key) {
          case 'catalogName':
            return {
              label: `${MetaCnNameMap['Catalog']}名称`,
              value: info.catalogName
            };
          case 'schemaName':
            return {
              label: `${MetaCnNameMap['Schema']}名称`,
              value: info.schemaName
            };
          case 'tableName':
            return {
              label: `${MetaCnNameMap['Table']}名称`,
              value: info.name
            };
          case 'tableType':
            return {
              label: '表类型',
              value: info.tableType
            };
          case 'dataSourceFormat':
            return {
              label: '数据源格式',
              value: info.dataSourceFormat === 'DORIS' ? 'DORIS 内表' : info.dataSourceFormat
            };
          case 'storageLocation':
            return {
              label: '存储路径',
              value: info.storageLocation
            };
          case 'createdAt':
            return {
              label: '创建时间',
              value: info.createdAt
            };
          case 'createdBy':
            return {
              label: '创建人',
              value: userList.find((item) => item.id === info.createdBy)?.name || info.createdBy
            };
          case 'updatedAt':
            return {
              label: '修改时间',
              value: info.updatedAt
            };
          case 'updatedBy':
            return {
              label: '最近修改人',
              value: userList.find((item) => item.id === info.updatedBy)?.name || info.updatedBy
            };
          default:
            return null;
        }
      })
      .filter(Boolean);
  }, [catalogType, dataInfo, userList, MetaCnNameMap, tableInfoFieldsMap]);

  // 获取详情
  const {loading, run: getTableDetail} = useRequest(
    async () => {
      // 获取 catalog 类型，根据类型获取详情展示形式，system 不需要请求
      if (catalog !== CatalogType.SYSTEM) {
        const catalogDetail = await http.getCatalogDetail(workspaceId, catalog);
        setCatalogType(catalogDetail?.result?.catalog?.type);
      }
      const res = await http.getTableDetail(workspaceId, fullName);
      setDataInfo(res.result);
    },
    {
      manual: true
    }
  );

  // 字段信息 表格的 Datasource
  const [filterColumnVal, setFilterColumnVal] = useState('');
  const tableData = useMemo(() => {
    const data = dataInfo?.columns || [];
    return data.filter((item) => ~item.name.indexOf(filterColumnVal));
  }, [dataInfo, filterColumnVal]);

  // 初始化
  useEffect(() => {
    catalog && schema && node && getTableDetail();
  }, [catalog, schema, node]);

  // tab 切换
  const onTabChange = useCallback(
    (tabkey) => {
      changeUrlFun((preState) => ({...preState, tab: tabkey}));
    },
    [changeUrlFun]
  );

  // 更新描述
  const onChangeDescript = useCallback(
    async (text: string) => {
      await http.patchTable(workspaceId, fullName, {comment: text});
      getTableDetail();
    },
    [dataInfo, setDataInfo]
  );

  // 重命名 & 删除
  const [showRenameModal, setRenameModal] = useState<boolean>(false);
  const renameSuccessFun = useCallback(
    (formData: {name: string}) => {
      changeUrlFunReplace((preState) => ({...preState, node: formData.name}), true);
    },
    [changeUrlFunReplace]
  );
  const removeSuccessFun = useCallback(() => {
    changeUrlFunReplace((preState) => ({...preState, node: '', type: ''}), true);
  }, [changeUrlFunReplace]);
  const onDropdownClick = useCallback(
    (key) => {
      if (key === 'rename') {
        setRenameModal(true);
      } else if (key === 'remove') {
        ModalRemoveFun({
          fullName,
          name: node,
          title: 'Table',
          requestFun: http.deleteTable,
          successFun: removeSuccessFun,
          workspaceId: workspaceId
        });
      }
    },
    [node, removeSuccessFun]
  );

  // 新建
  const onCreateClick = useCallback(() => {
    console.log('新建 :>> 一期不支持创建 Table');
  }, []);

  const renameNameRules = useMemo(() => {
    const ruleInfo = ruleMapByCatalog[catalogType];
    return [
      {
        validator: async (_, value) => {
          // 校验特殊字符和长度限制
          if (!ruleInfo.rule.test(value)) {
            return Promise.reject(new Error(ruleInfo.text));
          }
          // 异步校验Volume名称是否重复，复用查询接口 silent模式
          const res = await http.getTableDetail(workspaceId, `${catalog}.${schema}.${value}`, true);
          if (res.success && res.result?.id) {
            return Promise.reject(new Error(`该${MetaCnNameMap['Table']}名称已存在，请重新输入`));
          }
          return Promise.resolve();
        }
      }
    ];
  }, [catalog, catalogType, schema, workspaceId]);

  const renderTab = useMemo(() => {
    const config = {
      [PanelEnum.DETAIL]: <InfoPanel infoList={infoList} title="基本信息" />,
      [PanelEnum.OVERVIEW]: (
        <div>
          <DescriptionEdit
            text={dataInfo?.comment || ''}
            onChangeText={onChangeDescript}
            hasEdit={!isBuildInCatalog(catalog as CatalogType)} // 内置的 catalog 不允许编辑
            authList={dataInfo?.privileges || []}
          />
          <h2 className="title-head">字段信息</h2>
          <Search
            className="table-overview-search"
            onChange={(e) => setFilterColumnVal(e.target.value)}
            allowClear
            placeholder="请输入字段名称查询"
          />
          <Table dataSource={tableData} columns={columns} loading={loading} />
        </div>
      ),
      [PanelEnum.PERMISSION]: (
        <PermissionManage
          resourceType={ResourceType.Table}
          resourceId={fullName}
          hasInheritedFrom
          name={node}
          onSuccess={getTableDetail}
        />
      )
    };
    return config[tab];
  }, [
    catalog,
    dataInfo?.comment,
    dataInfo?.privileges,
    fullName,
    infoList,
    loading,
    node,
    onChangeDescript,
    tab,
    tableData,
    getTableDetail
  ]);

  return (
    <div className="work-meta-table-panel">
      {/* 标题导航操作栏 */}
      <MetaActionBar
        catalog={catalog}
        icon={<IconSvg type="meta-table" size={20} color="#fff" />}
        title={node as string}
        dropdownMenu={dropdownMenu}
        onDropdownClick={onDropdownClick}
        authList={dataInfo?.privileges || []}
        hiddenCreate
        // createText="新建 Table"
        // onCreateClick={onCreateClick}
        // hiddenCreate={catalogType === CatalogType.EDAP_DATALAKE}
        otherButton={
          catalogType === CatalogType.EDAP_DATALAKE ? (
            <Button
              onClick={() =>
                window.open(
                  `${edapIframeSrc}#/meta-data/manage?type=EDAP&topic=DataLake&database=${schema}&table=${node}`,
                  '_blank'
                )
              }
            >
              去管理
            </Button>
          ) : null
        }
      />
      {/* Tabs */}
      <MetaTabs
        panesList={initialPanes}
        tab={tab}
        onTabChange={onTabChange}
        authList={dataInfo?.privileges}
      />
      {renderTab}
      {/** 重命名 Table 弹窗 */}
      <ModalRename
        visible={showRenameModal}
        setVisible={setRenameModal}
        fullName={fullName}
        nowValue={node}
        title="Table"
        requestFun={http.patchTable}
        successFun={renameSuccessFun}
        nameRules={renameNameRules}
      />
    </div>
  );
};
export default PanelTable;
