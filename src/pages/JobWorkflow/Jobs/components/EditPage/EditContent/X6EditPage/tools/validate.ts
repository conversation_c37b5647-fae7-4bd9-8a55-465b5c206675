import {Graph} from '@antv/x6';
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON>ey<PERSON><PERSON><PERSON>} from '@api/job';
import {
  ClusterTypeEnum,
  JobNodeTypeEnum,
  OperatorCategoryChineseMap,
  RightDrawerTypeEnum,
  SPLIT_STR
} from '@pages/JobWorkflow/constants';
import {RULE} from '@utils/regs';
import {CronExpressionParser} from 'cron-parser';
import {isNumber} from 'lodash';
import {operatorDetailMap} from '../../../globalVar';
import {IJsonNodeData, IJsonOperatorData, IWorkflowCheckStatus} from '../type';
import {IOperCategoryEnum} from '@api/metaRequest';

// 判断是否存在环
export const hasCycle = (graph: Graph, sourceId: string, targetId: string): boolean => {
  const visited = new Set<string>();

  function dfs(currentId: string): boolean {
    if (currentId === sourceId) return true; // 回到起点，形成闭环
    if (visited.has(currentId)) return false;
    visited.add(currentId);

    const outgoingEdges = graph.getOutgoingEdges(currentId) || [];

    return outgoingEdges.some((edge) => {
      const nextId = edge.getTargetCellId();
      return nextId && dfs(nextId);
    });
  }

  return dfs(targetId);
};

// 校验 cron 表达式
function isCronValid(cron: string): boolean {
  try {
    CronExpressionParser.parse(cron, {
      strict: false
    });
    return true;
  } catch (err) {
    return false; // 解析失败即为无效表达式
  }
}

// 校验全局参数 true 为有效 false 为无效
const checkGlobalParams = (globalParams: IKeyValue[]): boolean => {
  try {
    return (
      globalParams.every((item) => item.key || (!item.value && !item.key)) &&
      globalParams.filter((item) => item.key).every((item) => RULE.workflowGlobalParamsKey.test(item.key))
    );
  } catch (error) {
    return false;
  }
};
// 校验算子参数
const checkOperator = (operator: IJsonOperatorData, parentId: string): IWorkflowCheckStatus => {
  const {name, metaData, params} = operator;
  if (!name) {
    return {
      success: false,
      type: RightDrawerTypeEnum.OPERATOR_CONFIG,
      index: 0,
      message: '请填写算子名称',
      nodeId: parentId + SPLIT_STR + operator.id
    };
  }
  // 算子参数 id路径拼接
  const key = [metaData.catalogName, metaData.schemaName, metaData.operatorName, metaData.version].join(
    SPLIT_STR
  );
  const operatorDetail = operatorDetailMap.get(key);

  const requiredParams = operatorDetail?.execParams?.filter((item) => item.required);

  const paramsMap = new Map(params?.map((item) => [item.key, item.value]));
  // 校验算子参数
  if (requiredParams.length === 0 || requiredParams.every((item) => !!paramsMap.get(item.name))) {
    return {success: true};
  } else {
    return {
      success: false,
      type: RightDrawerTypeEnum.OPERATOR_CONFIG,
      index: 0,
      message: '请填写算子参数',
      nodeId: parentId + SPLIT_STR + operator.id
    };
  }
};
// 校验算子任务
const checkDataflowTask = (item: IJsonNodeData, errorNode: IWorkflowCheckStatus[]) => {
  if (!isNumber(item.taskParam.parallel)) {
    return {
      success: false,
      type: RightDrawerTypeEnum.TASK_CONFIG,
      index: 1,
      message: '请填写执行资源',
      nodeId: item.id
    };
  }
  if (!item.taskParam?.clusterList?.[0]?.clusterId) {
    return {
      success: false,
      type: RightDrawerTypeEnum.TASK_CONFIG,
      index: 1,
      message: '请填写执行资源',
      nodeId: item.id
    };
  }
  // 校验算子节点数量
  if (item?.operatorList?.length === 0) {
    errorNode.push({
      success: false,
      type: RightDrawerTypeEnum.TASK_CONFIG,
      index: 0,
      message: '请添加算子',
      nodeId: item.id
    });
  } else {
    // 校验算子节点是否存在 输入 和 输出
    let hasStartOperator = false;
    let hasEndOperator = false;
    // 校验算子节点
    item?.operatorList?.forEach((operator) => {
      errorNode.push(checkOperator(operator, item.id));
      const operatorKey = [
        operator.metaData.catalogName,
        operator.metaData.schemaName,
        operator.metaData.operatorName,
        operator.metaData.version
      ].join(SPLIT_STR);

      const operatorDetail = operatorDetailMap.get(operatorKey);
      if (operatorDetail?.category === IOperCategoryEnum.Source) {
        hasStartOperator = true;
      }
      if (operatorDetail?.category === IOperCategoryEnum.Sink) {
        hasEndOperator = true;
      }
    });
    if (!hasStartOperator || !hasEndOperator) {
      errorNode.push({
        success: false,
        type: RightDrawerTypeEnum.TASK_CONFIG,
        index: 0,
        message: `算子节点必须存在${OperatorCategoryChineseMap.SOURCE}和${OperatorCategoryChineseMap.SINK}`,
        nodeId: item.id
      });
    }
  }
};

const checkRayTask = (item: IJsonNodeData, errorNode: IWorkflowCheckStatus[]) => {
  if (!item.taskParam.codePath || !item.taskParam.entryPoint || !checkGlobalParams(item.taskParam.envVars)) {
    errorNode.push({
      success: false,
      type: RightDrawerTypeEnum.TASK_CONFIG,
      index: 0,
      message: '请填写基本信息',
      nodeId: item.id
    });
  }
  if (!item.taskParam?.clusterList?.[0]?.clusterId) {
    return {
      success: false,
      type: RightDrawerTypeEnum.TASK_CONFIG,
      index: 1,
      message: '请填写执行资源',
      nodeId: item.id
    };
  }
};

// 校验集成任务
const checkIntegrationTask = (item: IJsonNodeData, errorNode: IWorkflowCheckStatus[]) => {
  if (!item.taskParam?.integrationJobId) {
    errorNode.push({
      success: false,
      type: RightDrawerTypeEnum.TASK_CONFIG,
      index: 0,
      message: '请填写基本信息',
      nodeId: item.id
    });
  }
};

// 校验 notebook 任务
const checkNotebookTask = (item: IJsonNodeData, errorNode: IWorkflowCheckStatus[]) => {
  if (!item.taskParam?.jupyterFilePath) {
    errorNode.push({
      success: false,
      type: RightDrawerTypeEnum.TASK_CONFIG,
      index: 0,
      message: '请填写基本信息',
      nodeId: item.id
    });
  }

  if (!item.taskParam?.clusterList?.[0]?.clusterId) {
    errorNode.push({
      success: false,
      type: RightDrawerTypeEnum.TASK_CONFIG,
      index: 1,
      message: '请填写执行资源',
      nodeId: item.id
    });
  }
};
// 校验 spark jar 任务
const checkSparkJarTask = (item: IJsonNodeData, errorNode: IWorkflowCheckStatus[]) => {
  const {dependentLibraries, mainClass, sparkConf, envVars, mainClassArgs} = item.taskParam;
  if (
    dependentLibraries?.length === 0 ||
    !dependentLibraries?.every((item) => !!item) ||
    !mainClassArgs?.every((item) => !!item) ||
    !mainClass ||
    !checkGlobalParams(envVars)
  ) {
    errorNode.push({
      success: false,
      type: RightDrawerTypeEnum.TASK_CONFIG,
      index: 0,
      message: '请填写基本信息',
      nodeId: item.id
    });
  }

  // if (clusterType === ClusterTypeEnum.EPHEMERAL) {
  if (!item.taskParam?.templateId || !checkGlobalParams(sparkConf)) {
    errorNode.push({
      success: false,
      type: RightDrawerTypeEnum.TASK_CONFIG,
      index: 1,
      message: '请填写执行资源',
      nodeId: item.id
    });
  }
  // }
};

// 校验 依赖任务
const checkDependentTask = (item: IJsonNodeData, errorNode: IWorkflowCheckStatus[]) => {
  const {workspaceId, jobId} = item.taskParam;
  if (!workspaceId || !jobId) {
    errorNode.push({
      success: false,
      type: RightDrawerTypeEnum.TASK_CONFIG,
      index: 0,
      message: '请填写基本信息',
      nodeId: item.id
    });
  }
};

// 校验节点名称
const checkNode = (node: IJsonNodeData): IWorkflowCheckStatus => {
  const {name} = node;
  if (!name) {
    return {
      success: false,
      type: RightDrawerTypeEnum.TASK_CONFIG,
      index: 0,
      message: '请填写节点名称',
      nodeId: node.id
    };
  }

  return {success: true};
};

// 校验 json 数据
const checkJson = (str: string): IWorkflowCheckStatus => {
  let obj = null;
  try {
    obj = JSON.parse(str);
    const errorNode = [];
    // 校验任务节点
    obj.taskDefinitionList.forEach((item) => {
      errorNode.push(checkNode(item));
      switch (item.type) {
        case JobNodeTypeEnum.DATAFLOW_TASK:
          checkDataflowTask(item, errorNode);
          break;
        case JobNodeTypeEnum.RAY_TASK:
          checkRayTask(item, errorNode);
          break;
        case JobNodeTypeEnum.NOTEBOOK_TASK:
          checkNotebookTask(item, errorNode);
          break;
        case JobNodeTypeEnum.DEPENDENT_TASK:
          checkDependentTask(item, errorNode);
          break;
        case JobNodeTypeEnum.SPARK_JAR_TASK:
          checkSparkJarTask(item, errorNode);
          break;
        case JobNodeTypeEnum.FILE_INTEGRATION_TASK:
        case JobNodeTypeEnum.TABLE_INTEGRATION_TASK:
          checkIntegrationTask(item, errorNode);
          break;
      }
    });
    const result = errorNode.filter((item) => item.success === false);
    return result.length > 0 ? result[0] : {success: true};
  } catch (err) {
    return {success: false, type: RightDrawerTypeEnum.JOB_CONFIG, index: 0, message: '请填写正确JSON'};
  }
};

// 任务节点名称、全局参数、调度信息
export const checkJob = (jobConfig: IJob, checkJsonFlag: boolean = false): IWorkflowCheckStatus => {
  // 工作流名称未填写
  if (!jobConfig.name) {
    return {success: false, type: RightDrawerTypeEnum.JOB_CONFIG, index: 0, message: '请填写工作流名称'};
  }
  // 全局参数配置存在 value 有值 key 为空值
  if (jobConfig.globalParams) {
    if (!checkGlobalParams(jobConfig.globalParams)) {
      return {success: false, type: RightDrawerTypeEnum.JOB_CONFIG, index: 1, message: '全局参数配置错误'};
    }
  }
  // 调度信息未填写
  if (jobConfig?.scheduleConf?.crontab && !isCronValid(jobConfig?.scheduleConf?.crontab)) {
    return {success: false, type: RightDrawerTypeEnum.JOB_CONFIG, index: 2, message: '请填写正确调度信息'};
  }

  if (jobConfig.code) {
    try {
      JSON.parse(jobConfig.code);
    } catch (err) {
      return {success: false, type: RightDrawerTypeEnum.JOB_CONFIG, index: 0, message: '请填写正确JSON'};
    }
  }

  if (checkJsonFlag) {
    return checkJson(jobConfig.code);
  }
  return {success: true};
};
