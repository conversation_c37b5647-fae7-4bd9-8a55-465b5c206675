import {OperateArgs} from '@api/integration/type';
import {ConfigProvider} from 'acud';
import locale from 'acud/lib/locale/zh_CN';
import ReactDOM from 'react-dom/client';
import OfflineCollectCreate from '../OfflineCollectCreate';
import PreCheckModal from '../PreCheckModal';
import StartJobModal from '../StartJobModal';

// 弹窗类型
export enum ModalTypeEnum {
  StartJob = 'startJob',
  PreCheck = 'preCheck',
  Create = 'create'
}
/**
 * 命令式打开弹窗
 * @param params 参数  jobs: JobItem[], workspaceId: string, callback: () => void, navigate: (path: string) => void
 * 目前使用 会导致 添加组件脱离 父组件 的 context，重新配置 locale 等
 * 无法获取 父组件的 路由，传递 navigate
 * @returns
 */
export default function OnOpenModal(type: ModalTypeEnum, ...params: OperateArgs): void {
  const [jobs, workspaceId, callback, navigate] = params;
  const container = document.createElement('div');
  document.body.appendChild(container);

  const root = ReactDOM.createRoot(container);
  const afterClose = () => {
    root.unmount();
    container.remove();
  };

  const ModalComponent = {
    [ModalTypeEnum.StartJob]: (
      <StartJobModal
        jobs={jobs}
        workspaceId={workspaceId}
        callback={callback}
        navigate={navigate}
        afterClose={afterClose}
      />
    ),
    [ModalTypeEnum.PreCheck]: (
      <PreCheckModal
        jobs={jobs}
        workspaceId={workspaceId}
        callback={callback}
        navigate={navigate}
        afterClose={afterClose}
      />
    ),
    [ModalTypeEnum.Create]: (
      <OfflineCollectCreate callback={callback} navigate={navigate} afterClose={afterClose} />
    )
  };

  root.render(<ConfigProvider locale={locale}>{ModalComponent[type]}</ConfigProvider>);
}
