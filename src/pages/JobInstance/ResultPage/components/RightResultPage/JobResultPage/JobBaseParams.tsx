import {IJobInstance} from '@api/jobInstance';
import React from 'react';
import DescriptionList from '../../DescriptionList';
import {JobRunTriggerTypeChineseEnum} from '@api/job';
import {JobInstanceStatusMap} from '@pages/JobInstance';
import {formatSeconds} from '@utils/utils';

const JobBaseParams: React.FC<{
  jobInstance: IJobInstance;
}> = ({jobInstance}) => {
  const infoList = [
    {
      label: '工作流名称',
      value: jobInstance?.jobName
    },
    {
      label: '工作流ID',
      value: jobInstance?.jobId
    },
    {
      label: '运行记录ID',
      value: jobInstance?.jobInstanceId
    },
    {
      label: '运行状态',
      value: JobInstanceStatusMap[jobInstance?.jobStatus]?.label
    },
    {
      label: '调度时间',
      value: jobInstance?.scheduleTime
    },
    {
      label: '开始时间',
      value: jobInstance?.startTime
    },
    {
      label: '结束时间',
      value: jobInstance?.endTime
    },
    {
      label: '运行时长',
      value: formatSeconds(jobInstance?.durationSec)
    },
    {
      label: '运行类型',
      value: JobRunTriggerTypeChineseEnum[jobInstance?.triggerType]
    },
    {
      label: '运行用户',
      value: jobInstance?.runUsername
    },
    {
      label: '描述',
      value: jobInstance?.description
    }
  ];

  return (
    <>
      <DescriptionList infoList={infoList} colon={false} />
    </>
  );
};

export default JobBaseParams;
