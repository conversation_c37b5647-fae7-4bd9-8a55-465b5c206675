/**
 * DividerLayout 组件
 *
 * 该组件用于实现一个可拖拽的分栏布局，支持左右两侧内容的分割和宽度调整。
 * 左侧宽度可以通过拖拽中间的竖线进行调整，并且支持折叠和展开功能。
 * 如果开启本地保存功能，左侧宽度会存储在 localStorage 中，页面刷新后会自动恢复原保存宽度。
 * <AUTHOR>
 */

import React, {useState, useRef, useEffect} from 'react';
import {debounce} from 'lodash';
import cx from 'classnames';
import {Left} from '@baidu/xicon-react-bigdata';

import './index.less';

interface IDividerLayoutBase {
  initWidth?: number; // 左侧初始宽度
  minWidth?: number; // 左侧最小宽度
  maxWidth?: number; // 左侧最大宽度
  className?: string; // 自定义类名
  children: [React.ReactNode, React.ReactNode]; // 左、右子组件
}

interface IDividerLayoutWithLocalStore extends IDividerLayoutBase {
  isSaveLocalStore: true; // 开启本地保存
  saveLocalStoreKey: string; // 本地保存的 localStorage key
}

interface IDividerLayoutWithoutLocalStore extends IDividerLayoutBase {
  isSaveLocalStore?: false;
  saveLocalStoreKey?: never;
}

type IDividerLayout = IDividerLayoutWithLocalStore | IDividerLayoutWithoutLocalStore;

// 本地保存宽度（防抖处理）
const debounceSaveWidth = debounce((width: number, isSaveLocalStore: boolean, saveLocalStoreKey: string) => {
  if (isSaveLocalStore && saveLocalStoreKey) {
    window.localStorage.setItem(saveLocalStoreKey, width.toString());
  }
}, 100);

const klass = 'left-divider-content';

const DividerLayout = (props: IDividerLayout) => {
  const {
    initWidth = 300,
    minWidth = 300,
    maxWidth = 500,
    className,
    isSaveLocalStore = false,
    saveLocalStoreKey = '',
    children
  } = props;

  const [leftWidth, setLeftWidth] = useState(() => {
    if (isSaveLocalStore && saveLocalStoreKey) {
      return Number(window.localStorage.getItem(saveLocalStoreKey)) ?? initWidth;
    }
    return initWidth;
  }); // 左侧宽度初始值
  const [preWidth, setPreWidth] = useState<number>(); // 保存折叠前的宽度
  const [isDragging, setIsDragging] = useState(false); // 是否正在拖拽

  const boxRef = useRef<HTMLDivElement>(null); // 整个容器的 ref
  const dragLineRef = useRef<HTMLDivElement>(null); // 竖线的 ref

  const [x, setX] = useState(0); // 鼠标按下时的 x 坐标
  // 处理鼠标按下事件，开始拖拽
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    setIsDragging(true);
    setX(e.clientX);
  };

  // 处理鼠标移动事件，调整宽度
  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      const newLeftWidth = e.clientX - x + leftWidth;
      if (newLeftWidth < minWidth) {
        setLeftWidth(minWidth);
      } else if (newLeftWidth > maxWidth) {
        setLeftWidth(maxWidth);
      } else {
        setLeftWidth(newLeftWidth);
      }
    }
  };

  // 处理鼠标松开事件，结束拖拽
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleIconClick = () => {
    if (leftWidth === 0) {
      setLeftWidth(preWidth ?? initWidth);
    } else {
      setPreWidth(leftWidth);
      setLeftWidth(0);
    }
  };

  // 在 useEffect 中绑定和卸载事件
  useEffect(() => {
    const currentBox = boxRef.current as HTMLDivElement;
    if (currentBox && isDragging) {
      currentBox.addEventListener('mousemove', handleMouseMove);
      currentBox.addEventListener('mouseup', handleMouseUp);
    }
    return () => {
      currentBox.removeEventListener('mousemove', handleMouseMove);
      currentBox.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging]);

  useEffect(() => {
    debounceSaveWidth(leftWidth, isSaveLocalStore, saveLocalStoreKey);
  }, [leftWidth, isSaveLocalStore, saveLocalStoreKey]);

  return (
    <div className={cx(className, `${klass}`)} ref={boxRef}>
      <div
        className={cx(`${klass}-l`, {
          [`${klass}-l-min`]: leftWidth === 0
        })}
        style={{
          width: `${leftWidth}px`,
          display: leftWidth === 0 ? 'none' : 'flex',
          ...leftWidth !== 0 ? {flexDirection: 'column'} : {}
        }}
      >
        {children[0]} {/* 左侧插槽 */}
      </div>
      <div
        className={cx(`${klass}-divider`, {
          [`${klass}-dragging`]: isDragging,
          [`${klass}-zero`]: leftWidth === 0
        })}
      >
        <div className={`${klass}-divider__line`} ref={dragLineRef} onMouseDown={handleMouseDown}>
          <div className={`${klass}-divider__icon`} onClick={handleIconClick}>
            <Left theme="line" color="#ccc" size={18} strokeLinecap="round" strokeLinejoin="round" />
          </div>
        </div>
      </div>

      <div className={`${klass}-r`}>
        {children[1]} {/* 右侧插槽 */}
      </div>
    </div>
  );
};

export default DividerLayout;
