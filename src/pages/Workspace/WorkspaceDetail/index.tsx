/**
 * @file 工作空间详情
 * <AUTHOR>
 */

import {FC, useCallback, useEffect, useState, useMemo} from 'react';
import {useSearchParams, useNavigate} from 'react-router-dom';
import {<PERSON><PERSON>crumb, Tabs, Button, Space, Loading, Modal, toast, Tooltip} from 'acud';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import {useRequest} from 'ahooks';
import {Delete} from '@baidu/xicon-react-bigdata';
import {queryWorkspaceDetail, IWorkspace, deleteWorkspace} from '@api/workspace';
import DetailTab from './components/DetailTab';
import PermissionTab from './components/PermissionTab';
import IconSvg from '@components/IconSvg';
import {getWorkspacePermission} from '@api/auth';
import store, {IAppState} from '@store/index';
import {EWorkspaceStatus} from '@pages/Workspace/constants';
import styles from './index.module.less';
import RoleTab from './components/RoleTab';
import {useSelector} from 'react-redux';
import {Privilege} from '@api/permission/type';
import AuthButton from '@components/AuthComponents/AuthButton';
import {WorkspacePrivileges} from '@utils/auth';
import {workspaceMenus} from '@pages/index';
import urls from '@utils/urls';

const WorkspaceDetail: FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const id = searchParams.get('id');

  const [workspaceDetail, setWorkspaceDetail] = useState<IWorkspace>();
  const [activeKey, setActiveKey] = useState();

  // 判断能否进入该空间
  const openDisabled = useMemo(
    () => !workspaceDetail?.privileges.includes(Privilege.Browse),
    [workspaceDetail]
  );
  const spaceError = useMemo(() => workspaceDetail?.status === EWorkspaceStatus.ERROR, [workspaceDetail]);

  // 获取工作空间详情
  const getWorkspaceDetail = useCallback(() => {
    !!id && runWorkspaceDetail({id});
  }, [id]);

  // 进页面时，先获取工作空间详情
  useEffect(() => {
    getWorkspaceDetail();
  }, [getWorkspaceDetail]);

  const {run: runWorkspaceDetail, loading: queryWorkspaceDetailLoading} = useRequest(queryWorkspaceDetail, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        setWorkspaceDetail(res?.result);
      }
    }
  });

  // 删除工作空间
  const runDeleteWorkspace = useCallback(
    (params) => {
      return deleteWorkspace(params);
    },
    [deleteWorkspace]
  );

  // tab切换
  const handleTabChange = useCallback((activeKey) => {
    setActiveKey(activeKey);
  }, []);

  // 删除工作空间
  const handleDelete = useCallback(() => {
    Modal.confirm({
      title: '删除空间',
      content: '您是否确认删除当前空间？删除后相关作业及日志将无法恢复',
      okText: '删除',
      onOk() {
        return runDeleteWorkspace({id: workspaceDetail?.id}).then((res) => {
          if (res.success) {
            toast.success({message: '删除成功', duration: 5});
            navigate('/manage-workspace');
          }
        });
      },
      onCancel() {}
    });
  }, [workspaceDetail, runDeleteWorkspace]);

  // 跳转空间内工作区
  const jumpWorkspace = useCallback(
    (id) => {
      const privilegeValue = Object.fromEntries(
        WorkspacePrivileges.map((key: Privilege) => [key, workspaceDetail?.privileges?.includes(key)])
      );
      store.dispatch({
        type: 'globalAuth/updateWorkspacePermission',
        payload: privilegeValue
      });

      const link =
        workspaceMenus.filter((item) => item.isNavMenu).find((item) => privilegeValue?.[item.privilege])
          ?.key || urls.workArea;

      window.open(`${window.location.pathname}#${link}?workspaceId=${id}`, '_blank');
    },
    [workspaceDetail?.privileges]
  );

  const isShowPermissionAndRole = useMemo(
    () =>
      [Privilege.Modify, Privilege.FullControl].some((item) => workspaceDetail?.privileges?.includes(item)),
    [workspaceDetail?.privileges]
  );

  const isManage = useMemo(
    () => workspaceDetail?.privileges?.includes(Privilege.FullControl),
    [workspaceDetail?.privileges]
  );

  return (
    <div className={styles['workspace-detail-container']}>
      <Loading loading={queryWorkspaceDetailLoading} />

      <Breadcrumb>
        <Breadcrumb.Item>
          <a href={`${window.location.pathname}#/manage-workspace`}>工作空间</a>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <div className={styles['workspace-detail-container-breadcrumb-name']}>
            <Ellipsis tooltip={workspaceDetail?.name}>{workspaceDetail?.name}</Ellipsis>
          </div>
        </Breadcrumb.Item>
      </Breadcrumb>
      <div className={styles['workspace-detail-header']}>
        <div className={styles['workspace-detail-title']}>
          <Ellipsis tooltip={workspaceDetail?.name}>{workspaceDetail?.name}</Ellipsis>
        </div>
        <Space>
          <AuthButton
            isAuth={isManage}
            className={styles['workspace-detail-delete']}
            icon={<Delete theme="line" size={16} strokeLinejoin="round" />}
            onClick={handleDelete}
          ></AuthButton>
          <Tooltip
            title={spaceError ? '该空间状态为【不可用】，无法进入！' : openDisabled ? '暂无此空间权限' : ''}
            placement="topRight"
          >
            <Button
              type="primary"
              icon={<IconSvg type="open" />}
              onClick={() => {
                jumpWorkspace(id);
              }}
              disabled={spaceError || openDisabled}
            >
              打开
            </Button>
          </Tooltip>
        </Space>
      </div>

      <Tabs
        onChange={handleTabChange}
        activeKey={activeKey}
        className={styles['workspace-detail-tabs']}
        destroyInactiveTabPane
      >
        <Tabs.TabPane tab="详情" key="detail">
          <DetailTab workspaceDetail={workspaceDetail} getWorkspaceDetail={getWorkspaceDetail} />
        </Tabs.TabPane>
        {isShowPermissionAndRole ? (
          <>
            <Tabs.TabPane tab="用户" key="permission">
              <PermissionTab
                workspaceDetail={workspaceDetail}
                privilege={workspaceDetail?.privileges}
                getWorkspaceDetail={getWorkspaceDetail}
              />
            </Tabs.TabPane>
            <Tabs.TabPane tab="角色" key="role">
              <RoleTab privilege={workspaceDetail?.privileges} getWorkspaceDetail={getWorkspaceDetail} />
            </Tabs.TabPane>
          </>
        ) : null}
      </Tabs>
    </div>
  );
};

export default WorkspaceDetail;
