import {getEnv} from '@hooks/useEnv';
const {isPrivate} = getEnv();
export const FOLDER_LIMIT_LENGTH = 128;
export const FOLDER_NAME_REGEX = /^([a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5_\-.·]{0,127})?$/;
export const FOLDER_NAME_ERROR_MESSAGE =
  '⽀持中⽂、英⽂、数字、中划线、下划线、.·特殊字符，不能以中划线、下划线开头，⻓度为1～128个字符';

export const FOLDER_NAME_MAP = {
  HOME: '我的',
  ALL: '全部',
  TRASH: '回收站',
  USERS: '用户',
  SHARED: '共享'
};

export const PRIVILEGE_LIST = [
  {label: '管理', value: 'MANAGE'},
  {label: '编辑', value: 'MODIFY'},
  {label: '运行', value: 'EXECUTE'},
  {label: '查看', value: 'VIEW'}
];

export function validatePath(path: string) {
  // 获取字符串的字节长度
  function getByteLength(str: string) {
    return new TextEncoder().encode(str).length;
  }

  // 文件路径正则表达式
  // 不能出现/或者\，不能仅为.
  const pathFormatRegex = /^(?!\.{1}$)([^/\\]*)$/;

  if (typeof path !== 'string') {
    return false;
  }

  // 检查字节长度
  const byteLength = getByteLength(path);
  if (byteLength < 1 || byteLength > 254) {
    return false;
  }

  // 私有化环境，文件名不能包含冒号和空格
  if (isPrivate && /[\s:：]/.test(path)) {
    return false;
  }

  // 检查格式要求
  return pathFormatRegex.test(path);
}

export const FILE_NAME_ERROR_MESSAGE = isPrivate
  ? '文件名称长度必须在1～254字节之间，不能出现/或者\\，不能仅为.，不能包含空格和冒号'
  : '文件名称长度必须在1～254字节之间，不能出现/或者\\，不能仅为.';
