import {
  ClusterTypeEnum,
  EngineTypeEnum,
  JobNodeTypeEnum,
  RightDrawerTypeEnum
} from '@pages/JobWorkflow/constants';

// 节点分组
export interface IX6NodeData {
  [key: string]: any;
  isExpanded?: boolean;
  parentId?: string;
  type: JobNodeTypeEnum;
  name?: string;
}

// X6 对象节点数据结构
export interface IX6Node extends INodeLocation {
  id?: string;
  shape?: string;
  x?: number;
  y?: number;
  // 节点属性配置
  attrs?: object;
  // 节点 连接桩配置
  ports?: object;
  // 节点 子节点
  children?: string[];
  // 节点 父节点
  parent?: string;
  // 节点数据配置
  data?: IX6NodeData;
}

// X6 连线数据结构
export interface IX6EdgeData {
  id?: string;
  parent?: string;
  shape?: string;
  source?: IX6NodePortData;
  target?: IX6NodePortData;
  attrs?: object;
  zIndex?: number;
  router?: object;
}

// 连接的起点或者 终点
export interface IX6NodePortData {
  cell: string;
  port?: string;
}
// json  边 数据结构
export interface IJsonEdgeData {
  source: string;
  target: string;
}

export interface IJsonNodeClusterData {
  clusterId: string;
  engineType: EngineTypeEnum;
  clusterType: ClusterTypeEnum;
}
// json 节点 参数数据结构
export interface IJsonNodeParamsData {
  // 通用参数
  clusterList: IJsonNodeClusterData[];
  //JobNodeTypeEnum.DATAFLOW_TASK 任务参数
  parallel?: number;
  //JobNodeTypeEnum.RAY_TASK 任务参数
  codePath?: string;
  //JobNodeTypeEnum.DEPENDENT_TASK 任务参数
  integrationJobId?: string;
  //JobNodeTypeEnum.NOTEBOOK_TASK 任务参数
  jupyterFilePath?: string;
  //JobNodeTypeEnum.SPARK_JAR_TASK 任务参数
  dependentLibraries?: string[];
  mainClass?: string;
  mainClassArgs?: string[];
  clusterType?: ClusterTypeEnum;
  templateId?: string;
  sparkConf?: {key: string; value: string}[];

  //JobNodeTypeEnum.DEPENDENT_TASK 任务参数
  workspaceId?: string;
  jobId?: string;

  // 运行环境 参数 前端使用
  envVars?: {
    key: string;
    value: string;
  }[];
  entryPoint?: string;
}
export interface INodeLocation {
  position?: {
    x: number;
    y: number;
  };
  size?: {
    width: number;
    height: number;
  };
  zIndex?: number;
  visible?: boolean;
  taskStatus?: string;
  subTaskCode?: string;
  operatorIds?: string[];
}
// 边节点数据结构
export interface IJsonNodeData {
  id: string;
  name: string;
  type: JobNodeTypeEnum;
  description: string;
  taskParam: IJsonNodeParamsData;
  // 前端位置信息
  location?: INodeLocation;
  operatorList?: IJsonOperatorData[];
  operatorRelationList?: IJsonEdgeData[];
}

// 算子节点数据结构
export interface IJsonOperatorData {
  id: string;
  name: string;
  metaData?: {
    catalogName: string;
    schemaName: string;
    operatorName: string;
    version: string;
  };
  params?: {key: string; value: string; valueType: string}[];
  location?: INodeLocation;
}

// json 数据结构
export interface IJsonData {
  taskRelationList: IJsonEdgeData[];
  taskDefinitionList: IJsonNodeData[];
}
// 编辑节点数据结构  动态更新 x6
export interface IX6EditNodeData {
  id: string;
  name: string;
  statusBase?: string;
  statusParam?: string;
}

/**
 * 校验状态
 * success: 是否成功
 * type: 类型  index: 索引 是否需要选中
 * nodeId: 节点 id
 * message: 消息弹窗
 * errorNode: 错误节点 id 和 错误消息
 */
export interface IWorkflowCheckStatus {
  success?: boolean;
  type?: RightDrawerTypeEnum;
  nodeId?: string;
  index?: number;
  message?: string;
}
