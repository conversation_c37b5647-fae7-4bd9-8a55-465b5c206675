import {IOperVersionOneRes} from '@api/metaRequest';
import {Clipboard} from '@baidu/bce-react-toolkit';
import EditableContent from '@components/EditableContent';
import IconSvg from '@components/IconSvg';
import {SPLIT_STR} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import {setEditNodeData, setFormIsDirty} from '@store/workflow';
import {Button, Form, Input, Tooltip} from 'acud';
import React, {useEffect, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {IJsonOperatorData} from '../../EditContent/X6EditPage/type';
import {getGraph, nodeMap, operatorDetailMap} from '../../globalVar';
import {RULE} from '@utils/regs';
import {useMemoizedFn} from 'ahooks';

const OperatorBaseParams: React.FC = () => {
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const selectedNodeId = useSelector((state: IAppState) => state.workflowSlice.selectedNodeId);
  // 算子父节点id
  const operateParentId = useSelector((state: IAppState) => state.workflowSlice.operateParentId);
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  // 算子参数
  const [detail, setDetail] = useState<IOperVersionOneRes | null>(null);
  // 节点参数
  const [nodeParams, setNodeParams] = useState<IJsonOperatorData | null>(null);

  // 算子名称
  const [nameArr, setNameArr] = useState<string[]>([]);

  // 处理算子名称
  const getOperateName = useMemoizedFn(() => {
    const nameArr = [];

    const graph = getGraph();
    const children = graph?.getCellById(operateParentId)?.children?.map((item: any) => item.id);
    console.log(children);
    nodeMap.forEach((value, key) => {
      if (key !== selectedNodeId && children?.includes(key)) {
        nameArr.push(value.name);
      }
    });
    setNameArr(nameArr);
  });
  // 初始化表单
  useEffect(() => {
    const selectedNode = nodeMap.get(selectedNodeId) as IJsonOperatorData;
    getOperateName();
    console.log(selectedNode);
    // 算子参数 id路径拼接
    const key = [
      selectedNode?.metaData?.catalogName,
      selectedNode?.metaData?.schemaName,
      selectedNode?.metaData?.operatorName,
      selectedNode?.metaData?.version
    ].join(SPLIT_STR);
    const operatorDetail = operatorDetailMap.get(key);

    setDetail(operatorDetail);
    setNodeParams(selectedNode);
    const params = [];
    operatorDetail?.execParams.forEach((item) => {
      params.push({
        key: item.name,
        value: selectedNode?.params?.find((p) => p.key === item.name)?.value || item.defaultValue,
        valueType: item.type
      });
    });

    form.setFieldsValue({name: selectedNode?.name, params});
    // 等待更新后再校验 TODO 后期需要优化 目前 0 的话 还是会有时候没有校验
    setTimeout(() => {
      form.validateFields();
    }, 100);
  }, [selectedNodeId, form, getOperateName]);

  // 修改表单 更新全局变量
  const changeForm = async (_: any, values: any) => {
    const oldObj = nodeMap.get(selectedNodeId);
    const newObj = {...oldObj, ...values};
    nodeMap.set(selectedNodeId, newObj);

    dispatch(setEditNodeData({id: selectedNodeId, name: values.name}));

    // 设置表单脏
    dispatch(setFormIsDirty(true));
  };

  return (
    <>
      <Form
        inputMaxWidth={'100%'}
        labelAlign="left"
        layout="horizontal"
        colon={false}
        name="basic"
        form={form}
        labelWidth={70}
        onValuesChange={changeForm}
      >
        <div className={'form-title'}>
          <IconSvg size={16} type="job-instance-result" />
          <span className={'form-title-text'}>算子信息</span>
        </div>
        <Form.Item
          label="算子节点名称"
          name="name"
          rules={
            isEditing
              ? [
                  {required: true, message: '请输入算子节点名称'},
                  {pattern: RULE.workflowName, message: RULE.workflowNameText},
                  {
                    validator(rule, value) {
                      if (value && nameArr.includes(value)) {
                        return Promise.reject(new Error('任务节点名称已存在'));
                      }
                      return Promise.resolve();
                    }
                  }
                ]
              : []
          }
        >
          <EditableContent isEditing={isEditing}>
            <Input forbidIfLimit={true} limitLength={256} placeholder="请输入" allowClear />
          </EditableContent>
        </Form.Item>
        <Form.Item label="算子名称">
          {detail?.operatorName}
          <Tooltip title={detail?.comment || '-'}>
            <IconSvg className="ml-2" type="question" fill="none" size={16} />
          </Tooltip>
        </Form.Item>

        {/* <Form.Item label="算子 ID">
          {detail?.id}
          <Clipboard text={detail?.id} className={'inline-block'}>
            <Button icon={<IconSvg type="copy" size={14} />} type="actiontext" />
          </Clipboard>
        </Form.Item> */}
        <Form.Item label="节点 ID">
          {nodeParams?.id}
          <Clipboard text={nodeParams?.id} className={'inline-block'}>
            <Button icon={<IconSvg type="copy" size={14} />} type="actiontext" />
          </Clipboard>
        </Form.Item>
        <Form.Item label="算子组件标识">{detail?.category}</Form.Item>
        <Form.Item label="版本">{detail?.name}</Form.Item>
        <div className={'form-title'}>
          <IconSvg size={16} type="job-instance-result" />
          <span className={'form-title-text'}>算子参数</span>
        </div>
        <Form.List name="params">
          {(fields) => (
            <>
              {fields.map(({name, key, ...restField}, index) => (
                <div key={key}>
                  <Form.Item hidden {...restField} name={[name, 'valueType']}>
                    <Input />
                  </Form.Item>
                  <Form.Item hidden {...restField} name={[name, 'key']}>
                    <Input />
                  </Form.Item>

                  <Form.Item
                    label={
                      <Tooltip
                        title={`${detail?.execParams[index]?.name}：${detail?.execParams[index]?.comment}`}
                      >
                        <div style={{width: '75px'}} className="truncate">
                          {detail?.execParams[index]?.name}
                        </div>
                      </Tooltip>
                    }
                    {...restField}
                    name={[name, 'value']}
                    rules={[
                      {
                        required: isEditing && detail?.execParams[index]?.required ? true : false,
                        message: '请输入' + detail?.execParams[index]?.name
                      }
                    ]}
                  >
                    <EditableContent isEditing={isEditing}>
                      <Input placeholder="请输入" />
                    </EditableContent>
                  </Form.Item>
                </div>
              ))}
            </>
          )}
        </Form.List>
      </Form>
    </>
  );
};

export default OperatorBaseParams;
