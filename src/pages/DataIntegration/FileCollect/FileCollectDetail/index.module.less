.info{
    &-container{
        background-color: #fff;
        overflow-y: auto;
        margin: 8px;
        border-radius: 6px;
        border: 1px solid #D4D6D9;
        padding: 16px;
        width: 100%;
    }

    &-top{
        margin: 16px 0;
        display: flex;
        justify-content: space-between;
    }

    &-bottom{
        display: flex;
        align-items: center;
        margin-bottom: 16px;
    }

    &-title{
        font-weight: 500;
        font-size: 22px;
        line-height: 32px;
        color: #151B26;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    &-text{
        color: #84868C;
        line-height: 13px;
        height: 13px;
        flex-shrink: 0;
        padding-left: 8px;
        margin-left: 8px;
        border-left: 1px solid #D4D6D9;
        display: flex;
        align-items: center;

        &-ellipsis{
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            flex: 1;
            display: inline-block;
        }
    }
}

.source-name, .dest-name{
    height: 20px;
    line-height: 20px;
    border-radius: 22px;
    background-color: #F3F3F6;
    color: #151B26;
    padding: 0 6px;
    display: flex;
    align-items: center;
}

.dest-name{
    width: 132px;
}
