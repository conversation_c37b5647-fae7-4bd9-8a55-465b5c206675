import {WorkspaceContext} from '@pages/index';
import {Button, Input, Loading, Select} from 'acud';
import React, {memo, ReactNode, useCallback, useContext, useMemo, useState} from 'react';

import {
  getFolderPath,
  getWorkspaceFileList,
  GetWorkspaceFileListResult,
  getWorkspaceFileResult,
  getWorkspaceFolderList,
  GetWorkspaceFolderListResult
} from '@api/WorkArea';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import IconSvg from '@components/IconSvg';
import BreadCrumb from '@pages/WorkArea/components/BreadCrumb';
import {useMemoizedFn} from 'ahooks';
import './index.less';

const {Option} = Select;

/**
 * 文件节点类型 文件、文件夹、NOTEBOOK
 */
export enum FileNodeTypeEnum {
  FILE = 'FILE',
  TRASH = 'TRASH',
  FOLDER = 'FOLDER',
  NOTEBOOK = 'NOTEBOOK'
}

export enum FileTypeEnum {
  ALL = 'ALL',
  HOME = 'HOME',
  USERS = 'USERS',
  USER = 'USER',
  TRASH = 'TRASH',
  NORMAL = 'NORMAL'
}

interface FilePathSelectWorkareaProps {
  // 是否禁用输入框
  disabled?: boolean;
  // 选择文件类型
  selectNodeType?: FileNodeTypeEnum;
  // 选择文件后缀
  selectFileSuffix?: string[];
  // 隐藏的类型
  hiddenType?: FileTypeEnum[];
}

const PREFIX = 'file-path-select-workarea';

const prefixCls = (base: string) => `${PREFIX}-${base}`;

/**
 * 工作区目录路径选择器
 */
const FilePathSelectWorkarea: React.FC<FilePathSelectWorkareaProps> = ({
  selectNodeType = FileNodeTypeEnum.FILE,
  selectFileSuffix = [],
  hiddenType = [FileTypeEnum.TRASH],
  disabled = false,
  ...rest
}) => {
  const {workspaceId} = useContext(WorkspaceContext);

  // 下拉 搜索项 搜索当前目录下所有文件
  const [searchValue, setSearchValue] = useState<string>();
  // 完整的文件路径
  const [path, setPath] = useState<GetWorkspaceFolderListResult[]>([]);
  // 当前目录下所有文件目录
  const [pathFileOptions, setPathFileOptions] = useState<GetWorkspaceFileListResult[]>([]);
  // select 下拉框是否开启，通过浏览按钮控制
  const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);
  // 是否加载中
  const [loading, setLoading] = useState<boolean>(false);

  // 获取路径列表 面包屑
  const getPathListFn = useMemoizedFn(async (id?: string) => {
    if (id) {
      const data = await getFolderPath({workspaceId, id});
      setPath(data.result);
    }
  });

  // 获取文件列表
  const getFileListFn = useMemoizedFn(async (parentId?: string) => {
    setLoading(true);
    // 如果parentId为空，则获取HOME目录
    if (!parentId) {
      const folderData = await getWorkspaceFolderList({workspaceId, parentId: ''});
      const id = folderData.result.filter((item) => item.type === FileTypeEnum.HOME)[0].id;
      parentId = id;
      getPathListFn(id);
    }
    const data = await getWorkspaceFileList({workspaceId, parentId});
    setPathFileOptions(data.result);
    setLoading(false);
  });

  // 选中回调
  const onSelect = useMemoizedFn((item: GetWorkspaceFileListResult) => {
    setSearchValue('');
    if (item.nodeType === FileNodeTypeEnum.FOLDER) {
      getFileListFn(item.id);
      getPathListFn(item.id);
    } else {
      setDropdownOpen(false);
    }
  });

  // 下拉框 打开/关闭
  const onDropdownHandler = useMemoizedFn(() => {
    if (!dropdownOpen) {
      getWorkspaceFileResult({path: (rest as any)?.value, workspaceId}).then((res) => {
        getPathListFn(res.result?.parentId);
        getFileListFn(res.result?.parentId);
      });
    }
    setDropdownOpen((flag) => !flag);
  });

  // 无状态 展示内容
  const notFoundContent = useMemo(() => {
    return (
      <div className={prefixCls('blank-icon')}>
        {!searchValue ? (
          <>
            <div className={prefixCls('blank-icon-list')} />
            <div>无可选数据</div>
          </>
        ) : (
          <>
            <div className={prefixCls('blank-icon-search')} />
            <div>搜索结果为空，请更换关键字重新搜索</div>
          </>
        )}
      </div>
    );
  }, [searchValue]);

  // 面包屑 点击回调
  const handleCurrentDirChange = useMemoizedFn((item: GetWorkspaceFolderListResult) => {
    setSearchValue('');
    getFileListFn(item.id);
    setPath((arr) => arr.slice(0, arr.indexOf(item) + 1));
  });

  // 下拉框渲染 面包屑 搜索框 下拉框
  const dropdownRender = useMemoizedFn((menu: ReactNode) => {
    return (
      <div className={prefixCls('dropdown-content')}>
        <div className={prefixCls('dropdown-search')}>
          <Input
            value={searchValue}
            placeholder="请输入关键字搜索"
            allowClear
            onChange={(e) => setSearchValue(e.target.value)}
          />
        </div>
        <Loading loading={loading} />

        <div className={prefixCls('dropdown-breadcrumb')}>
          {
            <BreadCrumb
              sliceEndNum={path.length}
              path={path}
              onItemClick={handleCurrentDirChange}
              maxLength={3}
            />
          }
        </div>
        {menu}
      </div>
    );
  });

  // 图标映射
  const iconMap = {
    [FileNodeTypeEnum.FOLDER]: <IconSvg size={16} type="workarea-folder" color="#84868C" />,
    [FileNodeTypeEnum.FILE]: <IconSvg size={16} type="workarea-file" color="#151B26" />,
    [FileNodeTypeEnum.TRASH]: <IconSvg size={16} type="workarea-trash" color="#84868C" />,
    [FileNodeTypeEnum.NOTEBOOK]: <IconSvg size={16} type="nb-notebook" color="#2468F2" fill="none" />
  };

  const disabledOptionFn = useMemoizedFn((item: GetWorkspaceFileListResult) => {
    let flag = false;
    if (selectFileSuffix && selectFileSuffix.length > 0 && item.nodeType === FileNodeTypeEnum.FILE) {
      flag = !selectFileSuffix.includes(item?.name?.split('.').pop() || '');
    }
    if (item.nodeType !== FileNodeTypeEnum.FOLDER && selectNodeType !== item.nodeType) {
      flag = true;
    }
    return flag;
  });

  return (
    <>
      <div className={PREFIX}>
        <Input disabled={disabled} className={prefixCls('input')} {...rest} />
        <div className={prefixCls('button')}>
          <Button disabled={disabled} onClick={onDropdownHandler}>
            {dropdownOpen ? '隐藏' : '浏览'}
          </Button>
        </div>
        <Select
          placement={'bottomRight'}
          className={prefixCls('select')}
          notFoundContent={notFoundContent}
          dropdownClassName={prefixCls('dropdown')}
          dropdownRender={dropdownRender}
          open={dropdownOpen}
          getPopupContainer={() => document.body}
          loading={loading}
          {...rest}
        >
          {pathFileOptions
            ?.filter((item) => !hiddenType.includes(item.type as FileTypeEnum))
            .filter((item) => {
              if (searchValue) {
                return item.name.includes(searchValue);
              }
              return true;
            })
            ?.map((item) => (
              <Option value={item.path} key={item.id} disabled={disabledOptionFn(item)}>
                <div className={prefixCls('select-item')} onClick={() => onSelect(item)}>
                  {iconMap[item.nodeType]}
                  <Ellipsis tooltip={item.name}>{item.name}</Ellipsis>
                </div>
              </Option>
            ))}
        </Select>
      </div>
    </>
  );
};

export default memo(FilePathSelectWorkarea);
