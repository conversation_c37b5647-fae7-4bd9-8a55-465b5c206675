import createEmptyPng from '@assets/png/workflow/create_empty.png';
import createJsonPng from '@assets/png/workflow/create_json.png';
import {JobModalTypeEnum} from '@pages/JobWorkflow/constants';
import React from 'react';
import styles from './index.module.less';
import useWorkspaceAuth from '@hooks/useWorkspaceAuth';
import {Privilege} from '@api/permission/type';
import AuthButton from '@components/AuthComponents/AuthButton';
import {TooltipType} from '@components/AuthComponents/constants';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import {Button} from 'acud';

const JobEmptyPage: React.FC<{
  setJobModal: (modal: {modalType?: JobModalTypeEnum; jobId?: string; jobName?: string}) => void;
}> = ({setJobModal}) => {
  // 权限列表
  const authList = useWorkspaceAuth([Privilege.WorkflowCreate, Privilege.WorkflowImport]);
  return (
    <div className={styles['empty-page']}>
      <div className={styles['content']}>
        <img className={styles['img']} src={createEmptyPng}></img>
        <div className={styles['title']}>创建空白工作流</div>
        <div className={styles['desc']}>自定义Json编辑创建 </div>
        <AuthComponents isAuth={authList[Privilege.WorkflowCreate]} tooltipType={TooltipType.Function}>
          <Button
            className={styles['btn']}
            type="primary"
            onClick={() => {
              setJobModal({modalType: JobModalTypeEnum.EMPTY});
            }}
          >
            立即创建
          </Button>
        </AuthComponents>
      </div>

      <div className={styles['content']}>
        <img className={styles['img']} src={createJsonPng}></img>
        <div className={styles['title']}>导入工作流</div>
        <div className={styles['desc']}>预置模版或者导入文件快速创建 </div>
        <AuthComponents isAuth={authList[Privilege.WorkflowImport]} tooltipType={TooltipType.Function}>
          <Button
            className={styles['btn']}
            type="primary"
            onClick={() => {
              setJobModal({modalType: JobModalTypeEnum.JSON});
            }}
          >
            立即导入
          </Button>
        </AuthComponents>
      </div>
    </div>
  );
};

export default JobEmptyPage;
