.connection-modal {
  min-height: 0;
  max-height: 800px;

  :global {
    .acud-modal-content {
      min-height: 0;
      max-height: 800px;

      .acud-modal-body {
        margin-right: 8px;
        margin-bottom: 24px;
      }

      .acud-steps {
        padding: 0 206px;
        margin-bottom: 20px;
      }

      .acud-form-item {
        margin-bottom: 20px;

        .acud-form-item-label {
          font-family: PingFangSC-Medium;
          font-size: 12px;
          color: #151b26;
          line-height: 20px;
        }

        .acud-form-item-control {
          max-width: 100% !important;
        }
      }
    }
  }

  .connection-modal-content {
    .step-hidden {
      display: none;
    }

    .connection-steps-content {
      padding-right: 12px;
      max-height: 615px;
      overflow-y: auto;

      .connection-steps-title {
        line-height: 22px;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 20px;
      }

      .step-content {
        .connection-type-group {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .connection-type-group-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
          }

          .connection-type-group-list {
            display: flex;
            flex-flow: row wrap;

            & > div {
              margin-right: 12px;
              margin-bottom: 16px;
            }

            & > div:nth-child(2n) {
              margin-right: 0;
            }

            & > div:nth-last-child(-n + 2) {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}

.connection-modal-step-1 {
  :global {
    .acud-modal-content {
      .acud-steps {
        padding: 0 32px;
        margin-bottom: 20px;
      }
    }
  }
}
