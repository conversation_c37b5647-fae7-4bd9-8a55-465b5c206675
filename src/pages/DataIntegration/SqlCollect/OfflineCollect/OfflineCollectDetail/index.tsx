import useUrlState from '@ahooksjs/use-url-state';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Divider, Dropdown, Link, Loading, Menu, Space, Tabs, Tooltip} from 'acud';
import cx from 'classnames';
import React, {useContext, useEffect, useMemo, useRef, useState} from 'react';
import {useNavigate} from 'react-router-dom';

import {getJobDetails} from '@/api/integration';
import {DetailTab} from '@/pages/DataIntegration/FileCollect/constants';
import {IJobItem, JobDetail} from '@api/integration/type';
import IconSvg from '@components/IconSvg';
import TextEllipsis from '@components/TextEllipsisTooltip';
import {IntegrationTab} from '@pages/DataIntegration/constants';
import {WorkspaceContext} from '@pages/index';
import {ConnectionTypeFlatMap} from '@pages/MetaData/Connection/constants';
import {formatTime} from '@utils/moment';
import urls from '@utils/urls';

import {JobStatusConfigMap, Operation, OperationShowType} from '../../constants';
import {authCheck, getOperationList, onPublish, onStart} from '../utils';
import TaskConfig from './components/TaskConfig';
import TaskList from './components/TaskList';
import styles from './index.module.less';
import AuthMenuItem from '@components/AuthComponents/AuthMenuItem';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import AuthButton from '@components/AuthComponents/AuthButton';
import {Privilege} from '@api/permission/type';

const {TabPane} = Tabs;

const OfflineCollectResult: React.FC = () => {
  const navigate = useNavigate();
  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);
  const [{jobId, tab}, setUrlState] = useUrlState({
    jobId: '',
    workspaceId: '',
    tab: DetailTab.ProcessLog
  });
  const [jobDetail, setJobDetail] = useState<JobDetail>();
  const [loading, setLoading] = useState(false);
  const listRef = useRef();

  // 初始化任务详情
  useEffect(() => {
    if (workspaceId && jobId) {
      setLoading(true);
      getJobDetails(workspaceId, jobId)
        .then((res) => {
          if (res.success) {
            setJobDetail(res.result as any);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [workspaceId, jobId]);

  // 刷新列表
  const refresh = () => {
    (listRef.current as any).refresh();
  };

  // 切换tab
  const onTabChange = (activeKey) => setUrlState({tab: activeKey});

  // 跳转至离线采集页
  const jumpOffline = () => {
    navigate(`${urls.integration}?tab=${IntegrationTab.Offline}`);
  };

  // 渲染操作项
  const renderOperation = () => {
    const operations = getOperationList(navigate, {
      status: jobDetail?.status,
      privileges: jobDetail?.privileges
    });
    const jobs = [{jobId, name: String(jobDetail?.name), workspaceId: workspaceId}] as IJobItem[];
    const menu = (
      <Menu>
        {operations[OperationShowType.DetailDropdown].map((item, index) => (
          <AuthComponents key={index} isAuth={item.isAuth}>
            <Menu.Item
              onClick={() =>
                item.callback(jobs, workspaceId, item.key === Operation.Delete ? jumpOffline : refresh)
              }
              disabled={item.disabled}
            >
              {item?.label}
            </Menu.Item>
          </AuthComponents>
        ))}
      </Menu>
    );
    return (
      <Space>
        <Dropdown overlay={menu} trigger={['click']}>
          <Tooltip title="更多">
            <Button className="square-button">
              <IconSvg type="more" size={16} />
            </Button>
          </Tooltip>
        </Dropdown>
        {operations[OperationShowType.DetailList].map((item, index) => (
          <AuthButton
            isAuth={item.isAuth}
            type="primary"
            key={index}
            onClick={() => item.callback(jobs, workspaceId, refresh, navigate)}
            disabled={item.disabled}
          >
            {item.label}
          </AuthButton>
        ))}
      </Space>
    );
  };

  // 来源图标
  const sourceIcon = useMemo(() => {
    const target = ConnectionTypeFlatMap[jobDetail?.sourceConfig?.sourceType];
    return (
      <IconSvg
        type={target?.icon}
        color={target?.color || undefined}
        size={16}
        className="bordered-circle-icon mr-[5px]"
      />
    );
  }, [jobDetail?.sourceConfig?.sourceType]);

  return (
    <div className={cx('db-workspace-wrapper', styles['info-container'])}>
      <Loading loading={loading} />
      <Breadcrumb className="mb-4">
        <Breadcrumb.Item>
          <Link onClick={jumpOffline}>库表采集</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <Link onClick={jumpOffline}>离线同步</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>任务详情</Breadcrumb.Item>
      </Breadcrumb>
      <div className={styles['info-top']}>
        <div className={cx(styles['info-title'], 'flex', 'items-center')}>
          <TextEllipsis tooltip={jobDetail?.name}>{jobDetail?.name || '-'}</TextEllipsis>
          <div
            className={cx(styles['info-status'], 'flex', 'items-center', 'ml-[12px]')}
            style={{
              background: JobStatusConfigMap[jobDetail?.status]?.bgColor,
              color: JobStatusConfigMap[jobDetail?.status]?.color
            }}
          >
            {JobStatusConfigMap[jobDetail?.status]?.label || '-'}
          </div>
        </div>
        <div>{renderOperation()}</div>
      </div>
      <div className={styles['info-bottom']}>
        <div className={styles['source-name']}>
          {sourceIcon}
          {jobDetail?.sourceConfig?.sourceConnectionId || '-'}
        </div>
        <IconSvg type="right" size={16} className="mr-[8px] ml-[8px]" />
        <div className={styles['dest-name']}>{jobDetail?.sinkConfig?.sinkType || '-'}</div>
        <Divider type="vertical" />
        <div className={styles['info-text']}>
          <Tooltip title="创建人">
            <IconSvg type="user" size={16} />
          </Tooltip>
          {jobDetail?.creatorName || '-'}
        </div>
        <Divider type="vertical" />
        <div className={styles['info-text']}>
          <Tooltip title="创建时间">
            <IconSvg type="time" size={16} />
          </Tooltip>
          {formatTime(jobDetail?.createTime)}
        </div>
        <Divider type="vertical" />
        <div className={cx(styles['info-text'], styles['info-desc'])}>
          <Tooltip title="任务描述">
            <IconSvg type="description" size={16} />
          </Tooltip>
          <TextEllipsis tooltip={jobDetail?.description}>{jobDetail?.description || '-'}</TextEllipsis>
        </div>
      </div>
      <div className={styles['result-page-tabs']}>
        <Tabs activeKey={tab} onChange={onTabChange}>
          <TabPane tab="运行记录" key={DetailTab.ProcessLog}>
            <TaskList ref={listRef} jobDetail={jobDetail} />
          </TabPane>
          <TabPane tab="任务信息" key={DetailTab.DetailConfig}>
            <TaskConfig jobDetail={jobDetail} />
          </TabPane>
          {/* <TabPane tab="统计信息" key="stat">
            <div style={{padding: 32, textAlign: 'center', color: '#84868c'}}>统计信息内容占位</div>
          </TabPane> */}
        </Tabs>
      </div>
    </div>
  );
};

export default OfflineCollectResult;
